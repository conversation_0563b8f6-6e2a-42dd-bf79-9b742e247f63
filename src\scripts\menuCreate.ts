import { DB } from '../config/DB';
import TherapistModel from '../models/Therapist.model';

async function main() {
    await DB.connect();

    const therapists = await TherapistModel.find({});
    for(const therapist of therapists){
        therapist.menus = {
            paymentTracker: true,
            paymentGateway: true
        }
        await therapist.save();
        console.log(`Updated menus for therapist: ${therapist._id}`)
    }

    console.log('All therapists updated successfully');

}

main();
