import { Schema, model, Document } from "mongoose";
import TherapistModel, { ITherapist } from "./Therapist.model";
import ClientModel, { IClient } from "./Client.model";
import ScheduleModel, { IRecurrenceDates, ISchedule } from "./Schedule.model";
import { number } from "joi";
import TransactionModel, { ITransaction } from "./Transaction.model";


export enum InvoiceCountryEnum {
    INDIA = "india",
    OTHER = "other"
}
export interface IInvoiceItem {
    description: string,
    quantity: number,
    amount: number,
    sac: string
}


export interface IInvoice extends Document {
    therapistId: Schema.Types.ObjectId | ITherapist,
    clientId: Schema.Types.ObjectId | IClient,
    scheduleId: Schema.Types.ObjectId | ISchedule
    scheduleRecId: Schema.Types.ObjectId | IRecurrenceDates
    transactionId: Schema.Types.ObjectId | ITransaction
    invoiceDate: Date
    from: {
        name: string,
        email: string,
        phone: string,
        address: string
    },
    to: {
        name: string,
        email: string,
        phone: string,
        address: string
    },
    therapistIdentifier: string
    invoiceCountry: InvoiceCountryEnum
    invoiceNumber: number
    invoiceSerialNumber: string
    items: IInvoiceItem[]
    itemTotal: number

    commission: number
    discount: number

    gatewayCharges: {
        gatewayFee: number,
        gatewayTax: number
    }

    gst: {
        cgst: number,
        sgst: number,
        igst: number
    }
    invoiceValue: number
    actualValue: number
    pdfLink: string
    payoutId: Schema.Types.ObjectId
    isCancelInvoice: boolean
}

const invoiceSchema = new Schema<IInvoice>({
    therapistId: {
        type: Schema.Types.ObjectId,
        ref: TherapistModel
    },
    transactionId: {
        type: Schema.Types.ObjectId,
        ref: TransactionModel
    },
    clientId: {
        type: Schema.Types.ObjectId,
        ref: ClientModel
    },
    scheduleId: {
        type: Schema.Types.ObjectId,
        ref: 'schedules'
    },
    scheduleRecId: {
        type: Schema.Types.ObjectId,
        ref: 'schedules.recurrenceDates'
    },
    invoiceDate: {
        type: Date
    },
    from: {
        name: String,
        email: String,
        phone: String,
        address: String
    },
    to: {
        name: String,
        email: String,
        phone: String,
        address: String
    },
    therapistIdentifier: String,
    invoiceCountry: {
        type: String,
        enum: Object.values(InvoiceCountryEnum)
    },
    invoiceNumber: Number,
    invoiceSerialNumber: String,
    items: [{
        description: String,
        quantity: Number,
        amount: Number,
        sac: String
    }],
    itemTotal: Number,
    commission: Number,
    gatewayCharges: {
        gatewayFee: Number,
        gatewayTax: Number
    },
    discount: Number,
    gst: {
        cgst: Number,
        sgst: Number,
        igst: Number
    },
    invoiceValue: Number,
    actualValue: Number,
    pdfLink: String,
    payoutId: {
        type: Schema.Types.ObjectId
    },
    isCancelInvoice: {
        type: Boolean,
        default: false
    }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "invoice"
    }
);

export default model<IInvoice>("invoice", invoiceSchema)