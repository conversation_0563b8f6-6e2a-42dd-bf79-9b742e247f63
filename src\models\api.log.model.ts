import { model, Schema, Document } from "mongoose";
import TherapistModel, { ITherapist } from "./Therapist.model";
import AdminModel, { IAdmin } from "./Admin.model";


export interface IApiLogs extends Document {
    therapist: Schema.Types.ObjectId | ITherapist,
    admin: Schema.Types.ObjectId | IAdmin,
    body: any,
    url: string,
    method: string,
    params: any,
    query: any,
    response: any
}

const apiLogs = new Schema<IApiLogs>({

    therapist: { type: Schema.Types.ObjectId, ref: TherapistModel },
    admin: { type: Schema.Types.ObjectId, ref: AdminModel },
    body: { type: Schema.Types.Mixed },
    url: { type: String },
    method: { type: String },
    params: { type: Schema.Types.Mixed },
    query: { type: Schema.Types.Mixed },
    response: { type: Schema.Types.Mixed }

}, {
    versionKey: false,
    timestamps: true,
    collection: "apiLogs"
})

export default model<IApiLogs>('apiLogs', apiLogs);