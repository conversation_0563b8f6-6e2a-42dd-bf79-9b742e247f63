// import { DB } from "../config/DB";
// import TherapistModel from "../models/Therapist.model";

// async function updateExistingTherapists() {
//     await DB.connect();

//     // Find all therapists without an identifier
//     const therapists = await TherapistModel.find({ identifier: { $exists: false } });

//     for (let therapist of therapists) {
//         // Generate identifier using the same logic as in your schema
//         let namePart = therapist.name ? therapist.name.substring(0, 3).toLowerCase() : '';
//         let randomPart = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
//         therapist.identifier = namePart + randomPart;

//         // Save the updated therapist
//         await therapist.save();
//     }

//     console.log('All existing therapists have been updated with identifiers.');
// }

// // Run the script
// updateExistingTherapists().catch(console.error);
