import SubscriptionModel, { SubscriptionStatusEnum, SubscriptionType } from "../../models/Subscription.model";

export default class SubscriptionDao {
    static async create(payload:any){
        return await SubscriptionModel.create(payload)
    }

    static async get(pageSize: number, skip: number){
        return await SubscriptionModel.find().skip(skip).limit(pageSize)
    }

    static async count(){
        return await SubscriptionModel.find().count()
    }
    
    static async getBy_id(_id: any, populate?: boolean){
        if(populate){
            return await SubscriptionModel.findOne({_id: _id}).populate('createdBy')
        }
        return await SubscriptionModel.findOne({_id: _id})
    }

    static async active(){
        return await SubscriptionModel.find({status: SubscriptionStatusEnum.ACTIVE, subscriptionType : {$ne: SubscriptionType.TRAIL}})
    }

    static async allActive(){
        return await SubscriptionModel.find({status: SubscriptionStatusEnum.ACTIVE})
    }

    // In subscription.dao.ts
    static async deleteById(_id: string) {
        return await SubscriptionModel.findByIdAndDelete(_id);
    }


}