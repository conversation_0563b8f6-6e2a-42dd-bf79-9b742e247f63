import cron from 'node-cron';
import { CONFIG } from '../config/environment';
import {google} from 'googleapis'
import { GoogleRequestService } from '../services/googleReq.service';
import { GoogleCalendarService } from '../services/googleCalendar.service';
import { TherapistDao } from '../lib/dao/therapist.dao';

export class CalendarCron{
    static async syncCalendar(){
        cron.schedule('*/10 * * * * *', async () => {
            const oauth2Client = new google.auth.OAuth2(
                CONFIG.clientId,
                CONFIG.clientSecret,
                CONFIG.clientRedirectUrl
            );
            const allTherapists: any = await TherapistDao.getAllTherapists()
            for(let therapist of allTherapists){
                const googleCalendar = await GoogleCalendarService.findByTherapist(therapist._id)
                if(googleCalendar){
                    const tokens = await GoogleRequestService.refreshAccessTokenRequest("/token", "POST", null, {
                        client_id: CONFIG.clientId,
                        client_secret: CONFIG.clientSecret,
                        refresh_token: therapist.refresh_token,
                        grant_type: 'refresh_token',
                    })
                    oauth2Client.setCredentials({ access_token: tokens.access_token });
                    const calendar = google.calendar({ version: 'v3', auth: oauth2Client });
        
                    const eventsResponse = await calendar.events.list({
                        calendarId: 'primary', // Use 'primary' for the user's primary calendar
                    });
        
                    const events = eventsResponse.data.items;               
                }
            }
        });
    }
}