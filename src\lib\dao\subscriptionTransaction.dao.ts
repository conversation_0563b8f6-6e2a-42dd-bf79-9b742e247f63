import SubscriptionTransactionModel from "../../models/SubscriptionTransaction.model";
import TherapistSubscriptionsModel from "../../models/TherapistSubscriptions.model";
import { paymentStatus } from "../enum/cashfree.enum";

export class SubscriptionTransactionDao{
    static async create(data: any){
        return await SubscriptionTransactionModel.create(data)
    }

    static async getByRzpOrder(orderId: string){
        return await SubscriptionTransactionModel.findOne({'rzpStatus.orderId': orderId}).populate('subscriptionId')
    }

    static async getTransactionSubscriptionPopulated(id:any){
        return await SubscriptionTransactionModel.find({_id:id}).populate('therapistId').populate('subscriptionId');
    }

    static async getActiveSubscriptionCount() {
        try {
            const currentDate = new Date();
            
            const activeSubscriptions = await TherapistSubscriptionsModel.aggregate([
                // Match only active subscriptions (not expired)
                {
                    $match: {
                        validTill: { $gte: currentDate }
                    }
                },
                // Lookup subscription details
                {
                    $lookup: {
                        from: "subscription",
                        localField: "subscriptionId",
                        foreignField: "_id",
                        as: "subscriptionDetails"
                    }
                },
                // Unwind the subscription details
                {
                    $unwind: "$subscriptionDetails"
                },
                // Group by subscription type and count
                {
                    $group: {
                        _id: "$subscriptionDetails._id",
                        name: { $first: "$subscriptionDetails.name" },
                        count: { $sum: 1 },
                        type: { $first: "$subscriptionDetails.subscriptionType" },
                        isAnnual: { $first: "$subscriptionDetails.isAnnual" },
                        isMonthly: { $first: "$subscriptionDetails.isMonthly" }
                    }
                },
                // Project the final format
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        count: 1,
                        type: 1,
                        isAnnual: 1,
                        isMonthly: 1
                    }
                }
            ]);

            return activeSubscriptions;
        } catch (err) {
            console.error(err);
            throw new Error("Unable to fetch active subscription count");
        }
    }
      

    static async getByTherapistId(id:any){
        return await SubscriptionTransactionModel.find({therapistId:id, paymentStatus: paymentStatus.COMPLETE}).populate('subscriptionId').sort({createdAt:-1});;
    }

    static async getByTherapistIdAll(id:any){
        return await SubscriptionTransactionModel.find({therapistId:id}).populate('subscriptionId').sort({createdAt:-1});;
    }
}
