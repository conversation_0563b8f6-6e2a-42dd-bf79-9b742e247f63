import { Schema, model, Document } from "mongoose";

export interface ITherapist extends Document {
  email: string;
  password: string; //not in use
  name: string;
  phone: string;
  panCard: string;
  gstNumber: string;
  address: {
    streetAddress: string;
    pincode: string;
    district: string;
    state: string;
  };
  therapist_name: string;
  practice_name: string;
  linkedin_url: string;
  hasUsedTrial: boolean;
  bankDetails: {
    bankAccountNo: number;
    ifscCode: string;
    branch: string;
    bankName: string;
    upiId: string;
    upiApprove: boolean;
    accountHolderName: string;
  };
  identifier: string;
  s3ProfilePhoto: boolean;
  settings: {
    emailNotification: boolean;
    weeklyReportsNotification: boolean;
    emailOnSessionConfirmation: boolean;
  };
  googleCalendarSynced: boolean;
  gCalLastSyncOn: Date;
  otpData: {
    otp: string;
    validTill: Date;
  };
  verificationDetails: {
    uploadedDocsCount: number;
    agePreference: string;
    genderPreference: string[];
    practicingTitle: string;
    clientLoad: string;
    yearsOfExperience: string;
    featuresNeed: string[];
    source: string[];
    sentForVerification: boolean;
    docs: string[];
  };
  isVerified: boolean;
  isDeleted: boolean;
  syncDate: Date;
  menus: {
    paymentTracker: boolean;
    paymentGateway: boolean;
  };
  isFromWebsite: boolean;
  pronouns: string;
  gender: string;
  minAge: number;
  maxAge: number;
  therapyTypes: string[];
  languages: string[];
  minFee: number;
  maxFee: number;
  location: string[];
  slotType: string[];
  timeZone: string;
  professionalQualification: string;
  values: string[];
  concerns: string[];
  practiceApproach: string;
  bookingURL: string;
  profilePicUrl: string;
  bookingMessage: string;
  fromPublicCalender: boolean;
}

const therapistSchema = new Schema<ITherapist>(
  {
    email: { type: String },
    password: { type: String },
    phone: { type: String },
    name: { type: String },
    panCard: { type: String },
    gstNumber: { type: String },
    address: {
      streetAddress: { type: String },
      pincode: { type: String },
      district: { type: String },
      state: { type: String },
    },
    therapist_name: { type: String },
    practice_name: { type: String },
    linkedin_url: { type: String },
    hasUsedTrial: { type: Boolean, default: false },
    identifier: {
      type: String,
      unique: true,
      default: function () {
        // Use the first 3 characters of the name and a random 3-digit number
        let namePart = this.name ? this.name.substring(0, 3).toLowerCase() : "";
        let randomPart = Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0");
        return namePart + randomPart;
      },
    },
    bankDetails: {
      bankAccountNo: Number,
      ifscCode: String,
      branch: String,
      bankName: String,
      accountHolderName: String,
      upiId: String,
      upiApprove: {
        type: Boolean,
        default: false,
      },
    },
    gCalLastSyncOn: Date,
    s3ProfilePhoto: { type: Boolean, default: false },
    settings: {
      emailNotification: {
        type: Boolean,
        default: true,
      },
      weeklyReportsNotification: {
        type: Boolean,
        default: true,
      },
      emailOnSessionConfirmation: {
        type: Boolean,
        default: true,
      },
    },
    googleCalendarSynced: { type: Boolean, default: false },
    otpData: {
      otp: String,
      validTill: Date,
    },
    verificationDetails: {
      uploadedDocsCount: { type: Number, default: 0 },
      agePreference: String,
      genderPreference: [{ type: String }],
      practicingTitle: String,
      clientLoad: String,
      yearsOfExperience: String,
      featuresNeed: [{ type: String }],
      source: [{ type: String }],
      sentForVerification: { type: Boolean, default: false },
      docs: [{ type: String }],
    },
    isVerified: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
    syncDate: Date,
    menus: {
      paymentTracker: { type: Boolean, default: true },
      paymentGateway: { type: Boolean, default: false },
    },
    isFromWebsite: { type: Boolean, default: false },
    pronouns: { type: String },
    gender: { type: String },
    minAge: { type: Number },
    maxAge: { type: Number },
    therapyTypes: [{ type: String }],
    languages: [{ type: String }],
    minFee: { type: Number },
    maxFee: { type: Number },
    location: [{ type: String }],
    slotType: [{ type: String }],
    timeZone: { type: String },
    professionalQualification: { type: String },
    values: [{ type: String }],
    concerns: [{ type: String }],
    practiceApproach: { type: String },
    bookingURL: { type: String },
    profilePicUrl: { type: String },
    bookingMessage: { type: String },
    fromPublicCalender: { type: Boolean, default: false },
  },
  {
    versionKey: false,
    timestamps: true,
    collection: "therapist",
  }
);

export default model<ITherapist>("therapist", therapistSchema);
