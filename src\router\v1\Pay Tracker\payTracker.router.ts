import { Router } from "express";
import { PayTrackerController } from "../../../controller/payTracker.controller";
import { Validate } from "../../../lib/validations/validate";
import { chargeSession, createPaytrackerSchema, updateAmount, updatedPaytrackerSchema } from "../../../lib/validations/paytracker.schema";



export default class PayTrackerRouter{
    public router: Router;

    constructor(){
        this.router = Router();
        this.routes()
    }

    public routes(): void {
        // GET
        this.router.get('/get/all',PayTrackerController.getAllPayTracker);
        this.router.get('/get',PayTrackerController.getPayTrackerByTherapistId);
        this.router.get('/get/stats',PayTrackerController.getStats);
        this.router.get('/get/clients',PayTrackerController.getTherapistClientsByName);
        this.router.get('/enums',PayTrackerController.getEnums);
        // POST
        this.router.post('/create',Validate(createPaytrackerSchema), PayTrackerController.createPayTracker);
        this.router.post('/send/reminder/:clientId/:paytrackerId', PayTrackerController.sendReminder)
        this.router.post('/update/amount/:paytracker',Validate(updateAmount), PayTrackerController.updateAmount);

        // UPDATE
        this.router.put('/update/:_id',Validate(updatedPaytrackerSchema), PayTrackerController.updatePayTracker);
        this.router.put('/change/status/:id', PayTrackerController.changePayTrackerStatus);
        this.router.put('/charge/session/:_id', Validate(chargeSession), PayTrackerController.cancellationCharge);

        // DELETE
        this.router.delete('/delete/:_id',PayTrackerController.deletePayTracker);

        // DOWNLOAD PAYMENT EXCEL FILE
        this.router.get('/download',PayTrackerController.downloadPayTrackerByTherapistId);
    }
}