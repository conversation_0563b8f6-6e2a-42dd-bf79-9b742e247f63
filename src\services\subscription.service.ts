import SubscriptionDao from "../lib/dao/subscription.dao";

export default class SubscriptionService {
    static async create(payload: any) {
        return await SubscriptionDao.create(payload);
    }

    static async get(pageSize: number, skip: number) {
        return await SubscriptionDao.get(pageSize, skip);
    }

    static async count() {
        return await SubscriptionDao.count();
    }

    static async getBy_id(_id: any, populate?: boolean) {
        return await SubscriptionDao.getBy_id(_id, populate);
    }

    static async active(){
        return await SubscriptionDao.active()
    }

    static async allActive(){
        return await SubscriptionDao.allActive()
    }

    // In subscription.service.ts
    static async deleteSubscription(_id: string) {
        return await SubscriptionDao.deleteById(_id);
    }

}