import moment from "moment";
import { DB } from "../config/DB";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ClientDao } from "../lib/dao/client.dao";
import {
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import { PayTrackerService } from "../services/payTracker.service";
import { ScheduleStatus } from "../models/Schedule.model";
import { TherapistDao } from "../lib/dao/therapist.dao";
import TransactionModel from "../models/Transaction.model";

async function paytrackerSchedules() {
  console.log("Paytracker script start!");
  await DB.connect();

  const currentDate = moment();

  const schedules = await ScheduleDao.getAllSchedulesInDateRange(currentDate);

  for (const schedule of schedules) {
    const client = await ClientDao.findClientById(schedule.clientId);
    if (!client) {
      continue;
    }
    const therapist = await TherapistDao.getTherapist(schedule.therapistId);
    if (!therapist) {
      continue;
    }
    for (const reccurance of schedule.recurrenceDates) {
      if (reccurance.status != ScheduleStatus.PENDING) {
        continue;
      }
      if (moment(reccurance.fromDate).isBefore(currentDate)) {
        continue;
      }
      const existPaytracker =
        await PayTrackerService.getPaytrackerByScheduleIdAndScheduleRecId(
          schedule._id,
          reccurance._id
        );
      if (existPaytracker) {
        console.log("Paytracker already exist");
        continue;
      }
      if (!therapist.menus.paymentTracker) {
        console.log(
          "Payment tracker is disabled for therapist" + therapist.name
        );
        continue;
      }
      let amount = 0;
      if (reccurance.amount) {
        amount = reccurance.amount || 0;
      }
      if (amount == 0) {
        amount = client.defaultSessionAmount
          ? Number(client.defaultSessionAmount)
          : 0;
      }

      const linkExist = await TransactionModel.findOne({
        scheduleRecId: reccurance._id,
      });
      if (!linkExist) {
        console.log(
          "Transaction not found for scheduleRecId: " + reccurance._id
        );
        continue;
      }

      const payload = {
        therapistId: therapist._id,
        scheduleId: schedule._id,
        scheduleRecId: reccurance._id,
        clientId: client._id,
        dueDate: reccurance.fromDate,
        amount: {
          currency: "INR",
          value: amount,
        },
        paymentType: reccurance.payLater
          ? PaymentTrackerTypeEnum.Post_Session
          : PaymentTrackerTypeEnum.Advance,
        status: PaymentTrackerStatusEnum.Still_Pending,
        paymentDate: undefined,
        isDeleted: false,
        tags: [client.name],
        isFine: false,
        cancellationFee: {
          currency: "INR",
          value: 0,
        },
      };

      const newPaytracker = await PayTrackerService.createPayTracker(payload);
      if (!newPaytracker) {
        console.error("paytracker not created");
      } else {
        console.log("Paytracker created");
      }
    }
  }
  console.log("Paytracker script end");
}

paytrackerSchedules();
