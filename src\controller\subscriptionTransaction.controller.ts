import express from 'express';
import SubscriptionService from '../services/subscription.service';
import { paymentMethod, paymentStatus } from '../lib/enum/cashfree.enum';
import { SubscriptionTransactionService } from '../services/subscriptionTransaction.service';
import RazorpayService from '../services/razorpay.service';
import TherapistSubscriptionService from '../services/therapistsubscription.service';
import moment from 'moment';
import TherapistModel from '../models/Therapist.model';
import CounterModel from '../models/Counter.model';

interface SubscriptionCount {
    _id: string;
    name: string;
    count: number;
    type: string;
    isAnnual: boolean;
    isMonthly: boolean;
}

export class SubscriptionTransactionController{

    static async createSubscriptionTransaction(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const therapistId = req.therapist._id
            const subscriptionId = req.params.planId;
            
            const subscription = await SubscriptionService.getBy_id(subscriptionId);
            if(!subscription){
                return res.status(404).send("NO Subscription found.")
            }

            const totalAmount = subscription.price;

            let order = await RazorpayService.createOrder(totalAmount, "INR");
            const isTrial = subscription.price === 0 && subscription.subscriptionType === "TRIAL PLAN";
            const data = {
                therapistId: therapistId,
                amount: totalAmount,
                amountReceived: 0,
                subscriptionId: subscription._id,
                paymentStatus: paymentStatus.PENDING,
                paymentMethod: paymentMethod.RAZORPAY,
                rzpStatus: {
                    orderId: order.id
                },
                isTrial,
            }
            const subscriptionTransaction = await SubscriptionTransactionService.create(data);
            if(!subscriptionTransaction){
                return res.status(403).send("Unable to create subscription transaction.")
            }

            res.send({order, message: "Payment Initiated"})
        } catch (err) {
            next(err);
        }
    }

    // In SubscriptionTransactionController
    static async getActiveSubscriptionCounts(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const activeSubscriptionCounts = await SubscriptionTransactionService.getActiveSubscriptionCount();
            
            // Calculate total active subscriptions with proper typing
            const totalActive = activeSubscriptionCounts.reduce((sum: number, subscription: SubscriptionCount) => {
                return sum + subscription.count;
            }, 0);
            
            res.status(200).send({
                subscriptions: activeSubscriptionCounts,
                totalActiveSubscriptions: totalActive,
                message: "Active subscription counts retrieved successfully"
            });
        } catch (error) {
            next(error);
        }
    }


    static async verifyPayment(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const {
                paymentId,
                orderId,
                paymentSignature
            } = req.body
            

            const verify = RazorpayService.verifyPayment(paymentId, orderId, paymentSignature);
            if(!verify){
                return res.status(403).send("Payment Verification Failed.")
            }

            const transaction:any = await SubscriptionTransactionService.getByRzpOrder(orderId);
            if(!transaction){
                return res.status(404).send("No Subscription Transaction found.")
            }

            transaction.rzpStatus.paymentId = paymentId;
            transaction.rzpStatus.paymentSignature = paymentSignature;
            transaction.paymentStatus = paymentStatus.COMPLETE;
            transaction.paymentDate = new Date();
            transaction.amountReceived = transaction.amount;
            const invoiceCounter = await CounterModel.findOneAndUpdate(
                { _id: "invoiceNumber" },
                { $inc: { seq: 1 } },
                { new: true, upsert: true }
              );
              
              const invoiceNumber = invoiceCounter.seq;
              transaction.invoiceNumber = invoiceNumber;

            await transaction.save();


            const subscriptionStartDate = await TherapistSubscriptionService.getActiveTillSubscriptionLastDate(transaction.therapistId);

            const subscriptionEndDate = moment(subscriptionStartDate).add(transaction.subscriptionId.validDays, 'days').toDate();

            const payload = {
                therapistId: transaction.therapistId,
                subscriptionId: transaction.subscriptionId._id,
                subscriptionTransactionId: transaction._id,
                validTill: subscriptionEndDate,
                validFrom: subscriptionStartDate
            }

            const subscription = await TherapistSubscriptionService.create(payload);
            
            if(!subscription){
                return res.status(403).send("Unable to create subscription.")
            }
            
            if (transaction?.isTrial && transaction?.therapistId) {
                await TherapistModel.findByIdAndUpdate(transaction.therapistId, { hasUsedTrial: true });
            }

            res.send({message: "Payment Verified."})
            
        } catch (err) {
            next(err);
        }
    }

    static async getSubscriptionsPopulated(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.Id;
            const subscriptions = await TherapistSubscriptionService.getSubscriptionsPopulated(id);
            if(!subscriptions){
                return res.status(400).send("Unable to get subscription");
            }
            res.status(200).send(subscriptions);
        } catch (error) {
            next(error);
        }
    }
    static async getTransactionSubscriptionPopulated(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.Id;
            const subscriptions = await SubscriptionTransactionService.getTransactionSubscriptionPopulated(id);
            if(!subscriptions){
                return res.status(400).send("Unable to get subscription");
            }
            res.status(200).send(subscriptions);
        } catch (error) {
            next(error);
        }
    }

    static async getByTherapistId(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.therapist._id.toString();
            const subscriptions = await SubscriptionTransactionService.getByTherapistId(id);
            if(!subscriptions){
                return res.status(400).send("Unable to get subscription");
            }
            res.status(200).send(subscriptions);
        } catch (error) {
            next(error);
        }
    }

    static async activateFreeTrial(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
          const therapistId = req.therapist._id;
          const subscriptionId = req.params.planId;
      
          const subscription = await SubscriptionService.getBy_id(subscriptionId);
          if (!subscription) {
            return res.status(404).send("No Subscription found.");
          }
      
          // Check if it's a valid trial plan
          const isTrial = subscription.price === 0 && subscription.name === "Free Trial";
          if (!isTrial) {
            return res.status(400).send("Selected plan is not a valid trial plan.");
          }
      
          const therapist = await TherapistModel.findById(therapistId);
          if (therapist?.hasUsedTrial) {
            return res.status(400).send("Therapist has already used the trial.");
          }
      
          // Create a dummy transaction record
          const transactionData = {
            therapistId,
            amount: 0,
            amountReceived: 0,
            subscriptionId: subscription._id,
            paymentStatus: paymentStatus.COMPLETE,
            paymentMethod: paymentMethod.FREE,
            isTrial: true,
            paymentDate: new Date(),
          };
      
          const transaction = await SubscriptionTransactionService.create(transactionData);
          if (!transaction) {
            return res.status(403).send("Unable to create trial transaction.");
          }
      
          const subscriptionStartDate = await TherapistSubscriptionService.getActiveTillSubscriptionLastDate(therapistId);
          const subscriptionEndDate = moment(subscriptionStartDate).add(subscription.validDays, 'days').toDate();
      
          const payload = {
            therapistId,
            subscriptionId: subscription._id,
            subscriptionTransactionId: transaction._id,
            validTill: subscriptionEndDate,
            validFrom: subscriptionStartDate,
          };
      
          const therapistSub = await TherapistSubscriptionService.create(payload);
          if (!therapistSub) {
            return res.status(403).send("Unable to create therapist subscription.");
          }
      
          await TherapistModel.findByIdAndUpdate(therapistId, { hasUsedTrial: true });
      
          res.status(200).send({ message: "Free trial activated successfully." });
        } catch (err) {
          next(err);
        }
      }
      

    static async getValidTillSubscription(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const therapistId = req?.therapist?._id || req.params.therapistId;
            const subscriptions = await TherapistSubscriptionService.getActiveSubscriptions(therapistId);
            if(subscriptions.length === 0){
                return res.status(200).send({valid: false, message: "No active subscription found."});
            }
            const validDate = subscriptions.map((subscription) => subscription.validTill).sort((a, b) => moment(b).valueOf() - moment(a).valueOf());
            const therapist = await TherapistModel.findById(therapistId).select('hasUsedTrial');
            const hasUsedTrial = therapist?.hasUsedTrial || false;

            res.status(200).send({valid: true, hasUsedTrial, message: `Subscription is valid till ${moment(validDate[0]).format("Do MMM YYYY")} `, validDate: validDate[0], validDays: moment(validDate[0]).diff(moment(), 'days')});
        } catch (error) {
            next(error);
        }
    } 

    static async getAllTherapistTransactions(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.id;
            const transactions = await SubscriptionTransactionService.getByTherapistIdAll(id);
            if(!transactions){
                return res.status(400).send("Unable to get transactions");
            }
            res.status(200).send(transactions);
        } catch (error) {
            next(error);
        }
    }

    static async refreshTransaction(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.id;
            const transaction: any = await SubscriptionTransactionService.getTransactionSubscriptionPopulated(id);
            if(transaction.length == 0){
                return res.status(400).send("Unable to get transactions");
            }

            const fetchedOrder = await RazorpayService.fetchOrder(transaction[0]?.rzpStatus?.orderId);
            if(!fetchedOrder){
                return res.status(400).send("Unable to razorpay status.");
            }
            const orderPayment = await RazorpayService.fetchOrderPayment(transaction[0]?.rzpStatus?.orderId);
            if(orderPayment.items.length === 0 || !orderPayment.items[0].id){
                return res.status(400).send("Payment Pending.");
            }

            const amountPaid = fetchedOrder.amount_paid;
            const paymentId = orderPayment?.items[0].id;
            const paymentDate = new Date(orderPayment?.items[0]?.created_at * 1000).toISOString()

            transaction[0].rzpStatus.paymentId = paymentId;
            transaction[0].paymentStatus = paymentStatus.COMPLETE;
            transaction[0].paymentDate = paymentDate;
            transaction[0].amountReceived = amountPaid;
            await transaction[0].save();

            const subscriptionStartDate = await TherapistSubscriptionService.getActiveTillSubscriptionLastDate(transaction[0].therapistId);

            const subscriptionEndDate = moment(subscriptionStartDate).add(transaction[0].subscriptionId.validDays, 'days').toDate();

            const payload = {
                therapistId: transaction[0].therapistId,
                subscriptionId: transaction[0].subscriptionId._id,
                subscriptionTransactionId: transaction[0]._id,
                validTill: subscriptionEndDate,
                validFrom: subscriptionStartDate
            }

            const subscription = await TherapistSubscriptionService.create(payload);
            if(!subscription){
                return res.status(403).send("Unable to create subscription.")
            }

            res.send({message: "Payment Verified."})
        } catch (error) {
            next(error);
        }
    }
    
}
