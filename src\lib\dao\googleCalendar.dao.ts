import GoogleCalendarModel from "../../models/GoogleCalendar.model";
import { CalendarEventDao } from "./calendarEvent.dao";

export class GoogleCalendarDao{
    static async create(payload: any){
        return await GoogleCalendarModel.create(payload)
    }

    static async findByTherapist(therapist: any){
        return await GoogleCalendarModel.findOne({therapist: therapist})
    }

    static async createEvent(event: any) {
        return await CalendarEventDao.createEvent(event);
    }

    static async update(_id: any, payload: any) {
        return await GoogleCalendarModel.findOneAndUpdate({_id: _id}, {$set: payload}, {new: true})
    }
    
    static async findby_id(_id:any){
        return await GoogleCalendarModel.findOne({_id: _id})
    }
}