import * as dotenv from "dotenv";
import path from "path";
dotenv.config();

export const CONFIG = {
  NODE_ENV: process.env.NODE_ENV,
  DB_CONNECTION_STRING: process.env.DB_STRING,
  BCRYPT_SALT_ROUNDS: process.env.BCRYPT_SALT_ROUNDS
    ? parseInt(process.env.BCRYPT_SALT_ROUNDS)
    : 10,
  jwt: {
    secret: "SDKFJ9#R3IO90U3@#9DSFIN",
    options: {
      // audience: 'https://example.io',
      expiresIn: "30d", // 1d
      // issuer: 'example.io'
    },
    cookie: {
      httpOnly: true,
      sameSite: true,
      signed: true,
      secure: true,
    },
  },
  cookie: {
    secret: "@#$@#4knshdf82#9382yrknjef9@#$",
  },
  clientId: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  clientRedirectUrl: process.env.REDIRECT_URL,
  cashfree: {
    cashFreeClientId: process.env.CASHFREE_CLIENT_ID,
    cashFreeClientSecret: process.env.CASHFREE_CLIENT_SECRET,
    cashFreeVersion: process.env.CASHFREE_VERSION,
  },
  googleBaseUrl: process.env.GOOGLE_BASERL,
  mailName: process.env.MAIL_NAME,
  mailApi: process.env.MAIL_API,
  mailFrom: process.env.MAIL_FROM,
  blockStorage: {
    accessKey: process.env.BUCKET_ACCESS_KEY,
    secretKey: process.env.BUCKET_SECRET_KEY,
    readOnlyAccessKey: process.env.BUCKET_ACCESS_KEY_READ_ONLY,
    readOnlySecretKey: process.env.BUCKET_SECRET_KEY_READ_ONLY,
    endPoint: process.env.S3_endPoint,
    bucket_name: process.env.BUCKET_NAME,
    public_bucket_name: process.env.PUBLIC_BUCKET_NAME,
  },
  brevo: {
    baseUrl: process.env.BREVO_BASEURL,
    apikey: process.env.BREVO_APIKEY,
    sendEMail: "/v3/smtp/email",
    sendWhatsApp: "/v3/whatsapp/sendMessage",
    whatsappSender: process.env.BREVO_WHATSAPP_SENDER,
  },
  stripe: {
    stripeSecretKEy: process.env.STRIPE_SECRET_KEY,
    stringProductName: process.env.STRIPE_PRODUCT_NAME,
    paymentSuccess: process.env.STRIPE_PAYMENT_SUCCESS,
    paymentCancel: process.env.STRIPE_PAYMENT_FAILED,
    stripeCurreny: process.env.STRIPE_CURRENCY,
  },
  razorpay: {
    key_id: process.env.RAZORPAY_KEY_ID || "",
    key_secret: process.env.RAZORPAY_KEY_SECRET || "",
  },
  accessType: process.env.ACCESS_TYPE || "offline",
  uploadsFolderPath: path.resolve(__dirname, "../../uploads"),
  cacheFolderPath: path.resolve(__dirname, "../../cache"),
  companyEmail: process.env.COMPANY_EMAIL,
  companyName: process.env.COMPANY_NAME,
  razorpayx: {
    baseUrl: process.env.RAZORPAYX_BASEURL || "https://api.razorpay.com/v1",
    createContact: "/contacts",
    createFundAccount: "/fund_accounts",
    createPayout: "/payouts",
  },
  frontendBaseUrl: process.env.FRONTEND_BASEURL,
};
