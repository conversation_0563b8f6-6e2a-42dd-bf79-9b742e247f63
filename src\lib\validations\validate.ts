import Jo<PERSON> from "joi";
import express from "express";
import { Response } from "../../util/response";
import { getErrorResponse } from "../../helper/error.handler";

export function Validate(validationSchema: Jo<PERSON>.Schema) {
  return function (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    let payload = req.body;
    if (req.body.onboardingData) {
      payload = JSON.parse(req.body.onboardingData);
    }
    let result = validationSchema.validate(payload);
    if (result.error) {
      return res
        .status(400)
        .send(getErrorResponse("VALIDATION_ERROR", result.error.message));
    } else {
      next();
    }
  };
}

export function ValidateParams(validationSchema: Joi.Schema) {
  return function (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    let result = validationSchema.validate(req.params);
    if (result.error) {
      return res.status(400).send(new Response({}, result.error.message, 400));
    } else {
      next();
    }
  };
}
