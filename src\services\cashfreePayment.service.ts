import { CONF<PERSON> } from "../config/environment";
import { ICashfree } from "../lib/interfaces/ICashfree";

const sdk = require('api')('@cashfreedocs-new/v3#z7c5zzlkqza7c0');


const credentials = {
    'x-client-id': CONFIG.cashfree.cashFreeClientId,
    'x-client-secret': CONFIG.cashfree.cashFreeClientSecret,
    'x-api-version': CONFIG.cashfree.cashFreeVersion
};


export class CashfreePayments {
    static async createCashFreePaymentLink(payload: ICashfree) {
        const link: any = await sdk.createPaymentLink(payload, credentials)
        if (link.status === 400) {
            return {
                status: link.status,
                data: link.data
            }
        }
        return link;
    }

    // static async batchTransfer() {
    //     const response = await payoutsInstance.transfers.requestBatchTransfer(
    //         {
    //             batchTransferId: "lad1234_akf",
    //             batchFormat: "UPI",
    //             deleteBene: 1,
    //             batch: [
    //                 { transferId: "13434", vpa: "7870831211@paytm", name: "md <PERSON><PERSON><PERSON>", phone: "7870831211", amount: 500, remarks: "payment" }
    //             ]
    //         });
    //     return response;
    //     // const data = batchTransfer.batchTransfer({
    //     //     batch: [
    //     //         // { transferId: "13434", vpa: "7870831211@upi", name: "md Arshad Ali", phone: "7870831211", amount: 500, remarks: "payment" }
    //     //     ],
    //     //     deleteBene: true,
    //     //     batchFormat: 'UPI',
    //     //     batchTransferId: 'AKD134_ADF'
    //     // }, { authorization: 'adfadfadf' })
    //     // return data
    // }
}