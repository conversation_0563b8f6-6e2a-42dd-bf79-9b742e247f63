import moment from "moment";
import TherapistSubscriptionDao from "../lib/dao/therapistsubscription.dao";

export default class TherapistSubscriptionService {

    static async getActiveSubscriptions(therapistId: any) {
        return await TherapistSubscriptionDao.getValidSubscriptions(therapistId);
    }

    static async getActiveTillSubscriptionLastDate(therapistId: any) {
        const subscriptions = await TherapistSubscriptionDao.getValidSubscriptions(therapistId);
        let validDate: any = new Date().toISOString();
        if(subscriptions.length == 0){
            return validDate
        }
        const validDates = subscriptions.map((subscription) => subscription.validTill).sort((a, b) => moment(b).valueOf() - moment(a).valueOf());
        validDate = validDates[0];
        
        return validDate
    }

    static async create(payload: any){
        return await TherapistSubscriptionDao.create(payload)
    }

    static async getSubscriptionsPopulated(id:any){
        return await TherapistSubscriptionDao.getSubscriptionsPopulated(id);
    }

    static async getPaidByTherapistId(therapistId:any){
        return await TherapistSubscriptionDao.getPaidByTherapistId(therapistId);
    }


}