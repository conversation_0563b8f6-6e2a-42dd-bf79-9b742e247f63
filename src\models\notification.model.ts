import { Schema, model } from "mongoose";
import TherapistModel from "./Therapist.model";

interface INotification extends Document {
    therapistId: Schema.Types.ObjectId
    isRead: boolean
    message: string
}

const notificationSchema = new Schema<INotification>({
    therapistId: { type: Schema.Types.ObjectId, ref: TherapistModel },
    isRead: { type: Boolean, default: false },
    message: String
},
    {
        versionKey: false,
        timestamps: true,
        collection: "notification"
    }
);

export default model<INotification>("notification", notificationSchema)