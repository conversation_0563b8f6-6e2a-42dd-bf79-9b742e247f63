import { PipelineStage, Types } from "mongoose";
import ScheduleModel from "../models/Schedule.model";
import {
  GoogleCalendarService,
  IAddEventPayload,
} from "./googleCalendar.service";
import { throwError } from "../util/response";
import { google } from "googleapis";
import { CONFIG } from "../config/environment";
import { v4 as uuidv4 } from "uuid";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { GoogleCalendarDao } from "../lib/dao/googleCalendar.dao";
import { createRRule } from "../helper/custom.helper";
import { ScheduleService } from "./schedule.service";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import calendarEventModel from "../models/calendarEvent.model";

interface finalLinkData {
  link?: string;
  calenderEventId?: string;
}

interface CalendarEvent {
  scheduleId?: any;
  scheduleRecId?: any;
  transactionId?: any;
  meetLink?: string;
  calenderEventId?: any;
  payTrackerId?: any;
  _id?: any;
  link?: any;
  start?: {
    dateTime?: any;
  };
}

interface GoogleCalendarEvent {
  link: string;
  calenderEventId: CalendarEvent[];
}

export class ScriptService {
  static async AddCalendarEventIdAndMeetLinkIntoOnlineSchedule(
    therapistId: string
  ) {
    // const pipeline: Array<PipelineStage> = [
    //   {
    //     $match: {
    //       therapistId: new Types.ObjectId(therapistId),
    //     },
    //   },
    //   {
    //     $addFields: {
    //       recurrenceDateExist: {
    //         $filter: {
    //           input: "$recurrenceDates", // Array to filter
    //           as: "recurrenceDate", // Variable name for array elements
    //           cond: {
    //             $in: [
    //               "$$recurrenceDate.status",
    //               ["confirmed", "rescheduled", "completed", "cancelled"],
    //             ], // Check if status is confirm or reschedule
    //           },
    //         },
    //       },
    //     },
    //   },
    //   {
    //     $addFields: {
    //       recurrenceDateCount: {
    //         $size: "$recurrenceDateExist",
    //       },
    //     },
    //   },
    //   {
    //     $match: {
    //       recurrenceDateCount: { $gt: 0 },
    //       recurrence: { $exists: true },
    //     },
    //   },
    // ];
    const schedules = await ScheduleModel.find({
      location: "online",
      therapistId: therapistId,
      recurrenceDates: {
        $elemMatch: { calenderEventId: { $exists: false } },
      },
      $expr: { $gt: [{ $size: "$recurrenceDates" }, 0] },
    });

    for (const schedule of schedules) {
      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        schedule.therapistId
      );
      const therapist: any = await TherapistDao.getTherapist(
        schedule?.therapistId
      );
      if (!googleCalendarData) {
        return throwError("No Google Calender Data found.", 404);
      }

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      const tokens = {
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
      };

      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });

      // const filteredRecurrenceDates = schedule.recurrenceDateExist.filter((i: any) => !i?.calendarEventId)

      // let recurrenceDate = schedule.recurrenceDate[0];
      // let lastRecurrenceDate =
      //   filteredRecurrenceDates[filteredRecurrenceDates?.length - 1];

      let recurrenceDate = schedule.recurrenceDates[0];
      let lastRecurrenceDate =
        schedule.recurrenceDates[schedule.recurrenceDates?.length - 1];

      const addEventPayload: IAddEventPayload = {
        emails: [schedule.email],
        summary: schedule.summary,
        location: schedule.location,
        description: schedule.description,
      };

      console.log("*******1");

      const rrule = createRRule(schedule.recurrence, lastRecurrenceDate.toDate);

      const recurranceData = {
        fromDate: recurrenceDate.fromDate,
        toDate: recurrenceDate.toDate,
        _id: recurrenceDate._id,
        rrule: rrule,
        data: schedule.recurrenceDates,
      };

      let googleCalenderEvent: GoogleCalendarEvent = {
        link: "",
        calenderEventId: [],
      };
      if (schedule.location === "online") {
        googleCalenderEvent = await GoogleCalendarService.addEventToCalender(
          therapistId,
          addEventPayload,
          recurranceData,
          schedule._id
        );
      }

      console.log("*******2");

      let paymentLink: any;
      let payTrackerId = undefined;
      let updateRecurrenceDate: any = {};

      for (const event of googleCalenderEvent.calenderEventId) {
        console.log("******3", event?._id);

        updateRecurrenceDate = await ScheduleDao.updateScriptRecurrenceDate(
          event?.scheduleId || "",
          event?.scheduleRecId || "",
          googleCalenderEvent?.link || "",
          event?._id || ""
        );
      }

      console.log("****4");

      // await this.DeleteCalenderEventFromSchedule(schedule?._id, calendar);
    }
  }

  static async DeleteCalenderEventFromSchedule(scheduleId: any, calendar: any) {
    const pipeline2: Array<PipelineStage> = [
      {
        $match: {
          scheduleId: scheduleId,
        },
      },
      {
        $match: {
          id: { $not: { $regex: "_" } }, // Matches documents where 'id' does not contain '_'
        },
      },
    ];
    const calendarEvents = await calendarEventModel.aggregate(pipeline2);

    for (const calendarEvent of calendarEvents) {
      if (calendarEvent?.id) {
        const event = await calendar.events.get({
          calendarId: "primary",
          eventId: calendarEvent?.id,
        });

        console.log("********");
        console.log(event?.data);
        console.log("********");

        if (event?.data && event?.data?.status !== "cancelled") {
          await calendar.events.delete({
            calendarId: "primary",
            eventId: event?.data?.id,
          });
        }
      }

      await calendarEventModel.findByIdAndDelete(calendarEvent?._id);
    }
  }
}

// async function DeleteCalenderEventManually(){
//   console.log('-----')
//   const oauth2Client = new google.auth.OAuth2(
//     CONFIG.clientId,
//     CONFIG.clientSecret,
//     // CONFIG.clientRedirectUrl
//   );

//   // let googleCalenderAccessToken = "******************************************************************************************************************************************************************************************************************************"

//   // let googleCalenderRefreshToken = "1//0gDCv8_SS1xt7CgYIARAAGBASNwF-L9Ir0fjLN2mBeoLLLbHVZd3lIY5lxcc5dPJNLLoFxcR7p0MREaqsXBTXUTOc3s7s9RPKdsk"
//   let googleCalenderAccessToken = "******************************************************************************************************************************************************************************************************************************"

//   let googleCalenderRefreshToken = "1//0gLjGrL-S6h9QCgYIARAAGBASNwF-L9IriRgWxUCiDDH1qzDTiOln0Ufiwvrrb6sQKQfgiPnoXP9IKXErXSQ4OJ5yERYzSRyr_Cw"

//   const tokens = {
//     access_token: googleCalenderAccessToken,
//     refresh_token: googleCalenderRefreshToken, // you'll only get this once, so store it securely
//   };

//   if (!tokens) {
//     throwError("No Google Calender Data found.", 404);
//     return {
//       syncable: [],
//       not_syncable: [],
//     };
//   }

//   oauth2Client.setCredentials(tokens);
//   const calendar = google.calendar({ version: "v3", auth: oauth2Client });

//   // let calenderEventId = "47vk7885ocivvmb59ueambu270" 
//   let calenderEventIds: any = [
//     "gbmvgmq2jm9u211204s5r17r04",
//     // "i66q28tvekkrfdpori1e11r8to",
//   ]
//   for (let i = 0; i < calenderEventIds.length; i++) {
//     const calenderEventId = calenderEventIds[i];
    
//     const response =await calendar.events.delete({
//       calendarId: "primary",
//       eventId: calenderEventId,
//     });

//     console.dir({response}, {depth: null})

//     console.log("Done ", i + 1)
//   }
//   // await calendar.events.delete({
//   //   calendarId: "primary",
//   //   eventId: calenderEventId,
//   // });

//   console.log('Done ALL')
// }  

// DeleteCalenderEventManually()

