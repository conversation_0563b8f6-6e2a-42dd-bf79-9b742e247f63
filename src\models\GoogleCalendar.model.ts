import { Schema, model } from "mongoose";
import TherapistModel from "./Therapist.model";

interface IGoogleCalendar extends Document{
    therapist: Schema.Types.ObjectId
    access_token: string,
    refresh_token: string,
    access_code: string,
    lastSyncDate: Date,
    calendarType: string
}

const googleCalendarSchema = new Schema<IGoogleCalendar>({
    therapist: {type: Schema.Types.ObjectId, ref: TherapistModel},
    access_token: {type: String},
    refresh_token: {type: String},
    access_code: {type: String},
    lastSyncDate: {type: Date},
    calendarType: {type: String}
},
{
    versionKey: false,
    timestamps: true,
    collection: "googleCalendar"
}
);

export default model<IGoogleCalendar>("googleCalendar", googleCalendarSchema)