import express from "express";
import { CONFIG } from "../config/environment";
import jwt from "jsonwebtoken";
import { throwError } from "../util/response";
import { TherapistService } from "../services/therapist.service";
import apiLogModel from "../models/api.log.model";

export function authMiddleware() {
  return async function (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      if (!req.headers.authorization) {
        return throwError("Invalid token", 400);
      }
      const decoded: any = jwt.verify(
        req.headers.authorization,
        CONFIG.jwt.secret
      );
      if (!decoded) {
        // Invalid token
        return res.status(401).send("Invalid Token");
      }
      const therapist = await TherapistService.getTherapist(decoded.id);

      if (!therapist) {
        return res
          .status(401)
          .send("Invalid Credentials. Try logging in again.");
      }

      if (therapist.isDeleted) {
        return res.status(401).send("Account Blocked.");
      }

      const apiLog = new apiLogModel({
        therapist: therapist._id,
        body: req.body,
        url: req.originalUrl,
        method: req.method,
        params: req.params,
        query: req.query,
      });
      await apiLog.save();
      req.therapist = therapist;
      req.apiLog = apiLog;

      next();
    } catch (error) {
      next(error);
    }
  };
}
