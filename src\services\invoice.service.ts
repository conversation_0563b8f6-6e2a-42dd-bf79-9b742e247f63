import { addLeading<PERSON><PERSON><PERSON> } from "../helper/custom.helper";
import { ClientDao } from "../lib/dao/client.dao";
import InvoiceDao from "../lib/dao/invoice.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { ScheduleStatus } from "../models/Schedule.model";
import { InvoiceCountryEnum } from "../models/invoice.model";
import { throwError } from "../util/response";



export default class InvoiceService {

    static async getLastInvoiceForTherapist(therapistId: any, isInternational: boolean) {
        return await InvoiceDao.getLastInvoiceForTherapist(therapistId, isInternational);
    }

    static async createInvoice(therapistId: any, clientId: any, scheduleId: any, scheduleRecId: any, isInternational: boolean, amount?:any, actualValue?: any) {

        const therapist = await TherapistDao.getTherapist(therapistId);
        if (!therapist) return throwError("Therapist not found", 404);

        const client = await ClientDao.getClientByTherapistId(clientId, therapistId);
        if (!client) return throwError("Client not found", 404);

        const schedule = await ScheduleDao.getById(therapistId, scheduleId);
        if (!schedule) return throwError("Schedule not found", 404);

        const scheduleRec = schedule.recurrenceDates.find((rec: any) => String(rec._id) == String(scheduleRecId));
        const recIndex = schedule.recurrenceDates.findIndex((rec: any) => String(rec._id) == String(scheduleRecId));
        if (!scheduleRec) return throwError("Schedule Recurrence not found", 404);

        if(scheduleRec.invoiceId) return throwError("Invoice already created", 400);

        let invoiceNumber = 1;
        const getLastInvoiceNumber = await InvoiceService.getLastInvoiceForTherapist(therapistId, isInternational);
        if (getLastInvoiceNumber.length > 0) {
            invoiceNumber = getLastInvoiceNumber[0].invoiceNumber + 1;
        }

        let serialNumber = therapist.identifier + "_" + addLeadingZeros(invoiceNumber, 5);
        if (isInternational) serialNumber = therapist.identifier + "_E_" + addLeadingZeros(invoiceNumber, 5);


        const session_amount = amount || scheduleRec.amount;

        if(!session_amount) return throwError("Session amount not found unable to create invoice", 404);

        let itemsTotal = 0;

        let gstValue = {
            cgst: 0,
            sgst: 0,
            igst: 0
        }

        itemsTotal = itemsTotal + Number(session_amount);

         
        let itemsData = [{
            description: `Session for ${schedule.description} with ${therapist.name}`,
            quantity: 1,
            amount: Number(session_amount),
            sac: "3e33344"
        }]
        if(scheduleRec.status === ScheduleStatus.CANCELLED) {
            itemsData = [{
                description: `Cancellation charge for ${schedule.description} with ${therapist.name}`,
                quantity: 1,
                amount: Number(session_amount),
                sac: "3e33344"
            }]

        }
            // gst.cgst += item.amount * 0.09;
            // gst.sgst += item.amount * 0.09;
            // gst.igst += item.amount * 0.18; 
        //     itemsTotal = itemsTotal + session_amount;
        //     return {
               
        //     }
        // })

        const invoiceValue = (itemsTotal + gstValue.cgst + gstValue.sgst + gstValue.igst);

        const payload = {
            therapistId: therapistId,
            clientId: clientId,
            scheduleId: scheduleId,
            scheduleRecId: scheduleRecId,
            from: {
                name: therapist.name,
                email: therapist.email,
                phone: therapist.phone,
                address: therapist.address
            },
            to: {
                name: client.name,
                email: client.email,
                phone: client.phone,
                address: client.address
            },
            therapistIdentifier: therapist.identifier,
            invoiceCountry: !isInternational ? InvoiceCountryEnum.INDIA : InvoiceCountryEnum.OTHER,
            invoiceNumber: invoiceNumber,
            invoiceSerialNumber: serialNumber,
            items: itemsData,
            itemTotal: itemsTotal,
            commission: 0,
            discount: 0,
            gst: {
                cgst: gstValue.cgst,
                sgst: gstValue.sgst,
                igst: gstValue.igst
            },
            invoiceValue: invoiceValue,
            actualValue: actualValue ,
            isCancelInvoice: scheduleRec.status == ScheduleStatus.CANCELLED ? true : false
        }

        const invoice = await InvoiceDao.create(payload);

        schedule.recurrenceDates[recIndex].invoiceId = invoice._id;
        await schedule.save();
        return invoice;
    }

    static async getAll(pageSize: any, skip: any, therapistId: any, clientId: any, isNotPaid: any, fromDate: any, toDate: any) {
        return await InvoiceDao.getAll(pageSize, skip, therapistId, clientId, isNotPaid, fromDate, toDate);
    }

    static async getCount( therapistId: any, clientId: any, isNotPaid: any, fromDate: any, toDate: any) {
        return await InvoiceDao.getCount(therapistId, clientId, isNotPaid, fromDate, toDate);
    }

    static async getInvoicesForLastMonth(timeBefore30Days: any){
        return await InvoiceDao.getInvoicesForLastMonth(timeBefore30Days);
    }

    static async getInvoiceById(id: any){
        return await InvoiceDao.getInvoiceById(id);
    }

    static async getInvoiceByScheduleRecId(scheduleRecId: any){
        return await InvoiceDao.getInvoiceByScheduleRecId(scheduleRecId);
    }

    static async getInvoicesForExport(therapistId:any, startDate:any, endDate:any){
        return await InvoiceDao.getInvoicesForExport(therapistId, startDate, endDate);
    }

}