import Jo<PERSON> from "joi";
import { DefaultMessage } from "./message/default.message";

export const CashFreeCreatePaymentSchema: Joi.Schema = Joi.object({
    customer_details: Joi.object().keys({
        customer_phone: Joi.string().required().messages(DefaultMessage.defaultRequired("customer_phone")),
        customer_email: Joi.string().required().messages(DefaultMessage.defaultRequired("customer_email")),
        customer_name: Joi.string().required().messages(DefaultMessage.defaultRequired("customer_name")),
    }).required().messages(DefaultMessage.defaultRequired("customer_details")),
    link_notify: Joi.object().keys({
        send_sms: Joi.boolean().required().messages(DefaultMessage.defaultRequired("send_sms"))
    }).required().messages(DefaultMessage.defaultRequired("link_notify")),
    link_id: Joi.string().required().messages(DefaultMessage.defaultRequired("link_id")),
    link_amount: Joi.number().required().messages(DefaultMessage.defaultRequired("link_amount")),
    link_currency: Joi.string().required().messages(DefaultMessage.defaultRequired("link_currency")),
    link_purpose: Joi.string().required().messages(DefaultMessage.defaultRequired("link_purpose")),
});