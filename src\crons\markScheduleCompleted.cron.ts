import cron from "node-cron";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ScheduleStatus } from "../models/Schedule.model";
import moment from "moment";
import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { CONFIG } from "../config/environment";
import { GoogleCalendarService } from "../services/googleCalendar.service";
import { google } from "googleapis";

export class MarkSchedueCron {
  static async markScheduleCompleted() {
    cron.schedule(" * * * * *", async () => {
      let allRecurrenceDates: any = [];
      const schedules = await ScheduleDao.getAllRecurrenceDates();
      for (let schedule of schedules) {
        allRecurrenceDates.push(schedule.recurrenceDates);
      }
      const recurrenceDatesPresent: any = [].concat(...allRecurrenceDates);
      const confirmedRecurrenceDates = recurrenceDatesPresent.filter(
        (recurrenceDate: any) =>
          [ScheduleStatus.CONFIRMED, ScheduleStatus.RESCHEDULED].includes(
            recurrenceDate.status
          ) && moment(recurrenceDate.toDate).isBefore(moment())
      );
      for (let recurrenceDate of confirmedRecurrenceDates) {
        const markCompleted = await ScheduleDao.markScheduleCompletedByRecId(
          recurrenceDate._id
        );
      }
    });
  }

  static async markScheduleCancelled() {
    cron.schedule("*/15 * * * *", async () => {
      const allTherapists = await TherapistDao.getAllTherapists();
      for (let therapist of allTherapists) {
        const googleCalendarData: any =
          await GoogleCalendarService.findByTherapist(therapist._id);

        if (!googleCalendarData) {
          continue;
        }

        const oauth2Client = new google.auth.OAuth2(
          CONFIG.clientId,
          CONFIG.clientSecret,
          CONFIG.clientRedirectUrl
        );

        const tokens = {
          access_token: googleCalendarData.access_token,
          refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
        };
        if (!tokens) {
          continue;
        }
        oauth2Client.setCredentials(tokens);

        const calendar: any = google.calendar({
          version: "v3",
          auth: oauth2Client,
        });

        const therapistCalendarEvents =
          await CalendarEventDao.getTherapistCalendarEvents(therapist._id);
        for (let calendarEvent of therapistCalendarEvents) {
          const calendarResponse = await calendar.events.get({
            calendarId: "primary",
            eventId: calendarEvent.id,
          });
          if (calendarResponse.data.status == "cancelled") {
            calendarEvent.status = calendarResponse.data.status;
            await calendarEvent.save();
            await ScheduleDao.cancelScheduleByCalendarEvent(
              therapist._id,
              calendarEvent._id
            );
          }
        }
      }
    });
  }
}
