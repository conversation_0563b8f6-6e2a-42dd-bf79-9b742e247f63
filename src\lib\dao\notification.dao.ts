import notificationModel from "../../models/notification.model";

export class NotificationDao {
    static async create(payload:any){
        return await notificationModel.create(payload)
    }

    static async update(_id: any, payload: any) {
        return await notificationModel.findByIdAndUpdate({_id: _id}, {
            $set: payload
        }, {new: true})
    }

    static async delete(_id: any) {
        return await notificationModel.findByIdAndDelete({_id: _id})
    }

    static async getAll() {
        return notificationModel.find({})
    }

    static async getByTherapistId(therapistId: any) {
        return notificationModel.find({therapistId: therapistId}).sort({createdAt: -1})
    }

    static async updateNotification(_id: any) {
        return notificationModel.findOneAndUpdate({_id: _id}, {
            $set: {
                isRead: true
            }
        }, {new: true}).sort({createdAt: -1})
    }
}