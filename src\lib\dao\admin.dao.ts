import AdminModel from "../../models/Admin.model";
import TherapistModel from "../../models/Therapist.model";

export class AdminDao {
    static async getByEmail(email: any) {
        return await AdminModel.findOne({ email: email })
    }

    static async updateBankDetails(therapistId: any, bankData: any) {
        return await TherapistModel.findByIdAndUpdate({ _id: therapistId }, {
            $set: {
                bankDetails: bankData
            }
        }, { new: true })
    }

    static async getById(_id: any) {
        return await AdminModel.findById(_id)
    }
}