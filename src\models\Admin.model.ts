import moment from "moment";
import { Schema, model, Document } from "mongoose";

export interface IAdmin extends Document {
    email: string,
    password: string,
    accessAllowedTill: Date
}

const adminSchema = new Schema<IAdmin>({
    email: { type: String },
    password: { type: String },
    accessAllowedTill: { type: Date, default: () => moment().add(6, "month").toDate() }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "admin"
    }
);

export default model<IAdmin>("admin", adminSchema)