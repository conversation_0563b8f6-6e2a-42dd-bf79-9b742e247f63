import calendarEventModel from "../../models/calendarEvent.model";

export class CalendarEventDao {
  static async createEvent(event: any) {
    return await calendarEventModel.create(event);
  }

  static async CheckEventLocally(eventIds: any) {
    return await calendarEventModel.find({ id: { $in: eventIds } }, "id");
  }

  static async getEventById(eventId: any) {
    return await calendarEventModel
      .findById(
        { _id: eventId },
        "hangoutLink creator attendees description summary id"
      )
      .populate("therapistId", "name email");
  }

  static async getByScheduleRecId(scheduleRecId: any) {
    return await calendarEventModel.findOne({ scheduleRecId: scheduleRecId });
  }

  static async findByTherapistAndRecId(
    therapistId: any,
    scheduleRecId: any,
    calendarEvenyId: any
  ) {
    return await calendarEventModel.findOne({
      _id: calendarEvenyId,
      therapistId: therapistId,
      scheduleRecId: scheduleRecId,
    });
  }

  static async getEventByEventId(eventId: any) {
    return await calendarEventModel.findOne({ id: eventId });
  }

  static async getTherapistCalendarEvents(therapistId: any) {
    return await calendarEventModel.find({ therapistId: therapistId });
  }

  static async findByDateRangeAndTherapistId(
    therapistId: any,
    fromDate: any,
    toDate: any
  ) {
    return await calendarEventModel
      .find({
        therapistId: therapistId,
        "end.dateTime": { $gte: fromDate, $lte: toDate },
      })
      .count();
  }

  static async findByDateRange(fromDate: any, toDate: any) {
    return await calendarEventModel
      .find({ "end.dateTime": { $gte: fromDate, $lte: toDate } })
      .count();
  }

  static async cancelCalenderEvent(eventId: any) {
    return await calendarEventModel.findByIdAndUpdate(
      { _id: eventId },
      {
        $set: {
          status: "cancelled",
        },
      }
    );
  }

  static async getByCalUid(iCalUid: any) {
    return await calendarEventModel.find({ iCalUID: iCalUid });
  }

  static async findby_id(_id: any) {
    return await calendarEventModel.findOne({ _id: _id });
  }

  static async getEventsByRecIds(scheduleRecIds: any) {
    return await calendarEventModel.find({
      scheduleRecId: { $in: scheduleRecIds },
    });
  }
}
