import { Router } from "express";
import { TherapistController } from "../../controller/therapist.controller";
import { Validate } from "../../lib/validations/validate";
import {
  ClientUpdateSchema,
  EventIdSchema,
  RecurrenceAmountSchema,
  SendOtpSchema,
  TherapistCalenderEventSchema,
  TherapistLoginSchema,
  UpdatePasswordSchema,
  UpdateTherapistSchema,
  VerificationTherapistSchema,
  VerifyOtpSchema,
  VerifyPayment,
  SyncCalendarClientsSchema,
  TherapistOnboardingSchema,
  TherapistProfileUpdateSchema,
  TherapistWorkingHoursSchema,
  TherapistSpecificWorkingHoursSchema,
  TherapistSpecificWorkingHoursUpdateSchema,
  SessionBookingSchema,
} from "../../lib/validations/therapist.schema";
import { authMiddleware } from "../../middleware/AuthMiddleware";
import { ScheduleSchema } from "../../lib/validations/schedule.schema";
import { upload } from "../../helper/fileUpload";
import { SubscriptionTransactionController } from "../../controller/subscriptionTransaction.controller";
import SubscriptionController from "../../controller/subscription.controller";
import PayTrackerRouter from "./Pay Tracker/payTracker.router";
import { ClientController } from "../../controller/client.controller";
import { WorkingHoursController } from "../../controller/workingHours.controller";

export default class TherapistRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    // GET
    this.router.get("/signup", TherapistController.signup);
    this.router.get("/get/:id", TherapistController.getTherapist);
    this.router.get(
      "/data",
      authMiddleware(),
      TherapistController.getTherapistData
    );
    this.router.get(
      "/vetificationStatus/:id",
      authMiddleware(),
      TherapistController.getTherapistVerificatonData
    );
    this.router.get(
      "/schedules/count",
      authMiddleware(),
      TherapistController.getSchedulesCount
    );
    this.router.get(
      "/schedules/search",
      authMiddleware(),
      TherapistController.searchSchedules
    );
    this.router.get(
      "/schedules",
      authMiddleware(),
      TherapistController.getSchedules
    );
    this.router.get(
      "/schedule/:id",
      authMiddleware(),
      TherapistController.getScheduleById
    );
    this.router.get(
      "/cancelled/session",
      authMiddleware(),
      TherapistController.cancelledSession
    );
    this.router.get(
      "/clients/email",
      authMiddleware(),
      ClientController.getClientByEmail
    );
    this.router.get(
      "/clients",
      authMiddleware(),
      TherapistController.getClients
    );
    this.router.get(
      "/regular",
      authMiddleware(),
      TherapistController.regularClient
    );
    this.router.get(
      "/client/schedules/:id",
      authMiddleware(),
      TherapistController.getClientSchedules
    );
    this.router.get(
      "/client/search",
      authMiddleware(),
      TherapistController.searchClient
    );
    this.router.get(
      "/client/count",
      authMiddleware(),
      TherapistController.clientCount
    );
    this.router.get(
      "/payments",
      authMiddleware(),
      TherapistController.getTherapistPayments
    );
    this.router.get(
      "/payment/stats",
      authMiddleware(),
      TherapistController.getTherapistPaymentStats
    );
    this.router.get(
      "/payment/search",
      authMiddleware(),
      TherapistController.searchTherapistPayment
    );
    this.router.get(
      "/dashboard/stats",
      authMiddleware(),
      TherapistController.getDashboardStats
    );
    this.router.get(
      "/schedules/transaction/get/:scheduleRecId",
      authMiddleware(),
      TherapistController.getTransactionByRecIdTherapistId
    );
    this.router.get(
      "/timezones",
      authMiddleware(),
      TherapistController.getTimeZones
    );
    this.router.get(
      "/schedules/client/:id",
      authMiddleware(),
      TherapistController.getScheduleClientData
    );
    this.router.get(
      "/client/check/:id",
      authMiddleware(),
      TherapistController.checkClientPendingPayments
    );
    this.router.get(
      "/export/transaction/csv",
      authMiddleware(),
      TherapistController.exportTransactionCSV
    );

    // this.router.get("/test", TherapistController.testingBrevo)

    this.router.get(
      "/getCalendarEvents",
      authMiddleware(),
      TherapistController.getCalendarEvents
    );

    this.router.get(
      "/getScheduleList",
      authMiddleware(),
      TherapistController.getScheduleList
    );

    this.router.get(
      "/calendar/resync",
      authMiddleware(),
      TherapistController.resyncNew
    );

    this.router.get(
      "/calendar/resync-v2/:therapistId",
      TherapistController.resyncV2
    );

    // this.router.get("/calendar/resync-new", authMiddleware(), TherapistController.resyncNew);
    this.router.get(
      "/subscription/get",
      authMiddleware(),
      SubscriptionController.getActiveSubscriptions
    );
    this.router.get(
      "/subscription/get/:id",
      authMiddleware(),
      SubscriptionController.getById
    );
    this.router.get(
      "/subscription/populated/get",
      authMiddleware(),
      SubscriptionTransactionController.getSubscriptionsPopulated
    );
    this.router.get(
      "/subscription/transaction/get",
      authMiddleware(),
      SubscriptionTransactionController.getTransactionSubscriptionPopulated
    );
    this.router.get(
      "/subscription/transaction/therapistId/get",
      authMiddleware(),
      SubscriptionTransactionController.getByTherapistId
    );
    this.router.get(
      "/subscription/transaction/therapistId/get",
      authMiddleware(),
      SubscriptionTransactionController.getByTherapistId
    );
    this.router.get(
      "/subscription/active/get",
      authMiddleware(),
      SubscriptionController.getPaidSubscriptions
    );
    this.router.get(
      "/subscription/valid",
      authMiddleware(),
      SubscriptionTransactionController.getValidTillSubscription
    );
    this.router.get(
      "/calendar/checkSynedStatus",
      authMiddleware(),
      TherapistController.checkCalenderSyncStatus
    );
    this.router.get(
      "/free-slot",
      authMiddleware(),
      TherapistController.RetriveFreeSlot
    );

    // POST
    // this.router.get("/create", TherapistController.createTherapist);
    this.router.post(
      "/login",
      Validate(TherapistLoginSchema),
      TherapistController.login
    );
    this.router.post("/create", TherapistController.addTherapist);
    this.router.post(
      "/schedules/create",
      authMiddleware(),
      Validate(ScheduleSchema),
      TherapistController.createSchedule
    );
    this.router.post(
      "/addEventToCalendar",
      authMiddleware(),
      Validate(TherapistCalenderEventSchema),
      TherapistController.addEventToCalendar
    );
    this.router.post(
      "/getSpecificCalendarEvents",
      authMiddleware(),
      Validate(EventIdSchema),
      TherapistController.getSpecificCalendarEvents
    );
    this.router.post(
      "/getSpecificCalendarEventClients",
      authMiddleware(),
      Validate(SyncCalendarClientsSchema),
      TherapistController.getSpecificCalendarEventClients
    );
    this.router.post(
      "/profile/upload",
      authMiddleware(),
      upload.array("upload"),
      TherapistController.uploadTherapistProfile
    );
    this.router.post(
      "/docs/upload",
      authMiddleware(),
      upload.array("upload"),
      TherapistController.uploadTherapistDocument
    );
    this.router.post(
      "sentEmailTherapist",
      authMiddleware(),
      TherapistController.sentEmailTherapist
    );
    this.router.post(
      "/reminder/send/:paymentId",
      authMiddleware(),
      TherapistController.sendReminder
    );
    this.router.post(
      "/schedule/reminder/:id",
      authMiddleware(),
      TherapistController.sendScheduleReminder
    );
    this.router.post(
      "/subscription/transaction/:planId",
      authMiddleware(),
      SubscriptionTransactionController.createSubscriptionTransaction
    );
    this.router.post(
      "/subscription/verify",
      authMiddleware(),
      Validate(VerifyPayment),
      SubscriptionTransactionController.verifyPayment
    );
    this.router.post(
      "/subscription/activate-trial/:planId",
      authMiddleware(),
      SubscriptionTransactionController.activateFreeTrial
    );
    this.router.post(
      "/google/events/update",
      authMiddleware(),
      TherapistController.rescheduleRecurringGoogleEvents
    );
    this.router.post("/get/document", TherapistController.getS3Document);
    this.router.post(
      "/file/upload",
      authMiddleware(),
      upload.single("upload"),
      TherapistController.uploadFile
    );
    this.router.post(
      "/menu/update",
      authMiddleware(),
      TherapistController.updateMenu
    );

    // PUT
    this.router.put(
      "/otp/send",
      Validate(SendOtpSchema),
      TherapistController.sendOtp
    );
    this.router.put(
      "/otp/verify",
      Validate(VerifyOtpSchema),
      TherapistController.verifyOtp
    );
    this.router.put(
      "/password/update",
      Validate(UpdatePasswordSchema),
      TherapistController.updatePassword
    );
    this.router.put(
      "/client/:id",
      authMiddleware(),
      Validate(ClientUpdateSchema),
      TherapistController.updateClient
    );
    this.router.put("/update/:id", TherapistController.updateTherapist);
    this.router.put(
      "/payment/recreate/:id",
      authMiddleware(),
      TherapistController.recreatePaymentLink
    );
    this.router.put(
      "/payment/create/:id",
      authMiddleware(),
      Validate(RecurrenceAmountSchema),
      TherapistController.createPaymentLink
    );
    this.router.put(
      "/data",
      authMiddleware(),
      Validate(UpdateTherapistSchema),
      TherapistController.updateTherapistData
    );
    this.router.put(
      "/verification",
      authMiddleware(),
      upload.array("upload"),
      Validate(VerificationTherapistSchema),
      TherapistController.updateTherapistVerification
    );
    this.router.put(
      "/reschedule/:id",
      authMiddleware(),
      TherapistController.reschedule
    );
    this.router.put(
      "/schedule/completed/:id",
      authMiddleware(),
      TherapistController.markScheduleCompleted
    );
    this.router.put(
      "/schedule/cancel/:id",
      authMiddleware(),
      TherapistController.cancelSchedule
    );
    this.router.put(
      "/schedule/cancelAll/:id",
      authMiddleware(),
      TherapistController.cancelAllSchedule
    );
    this.router.put(
      "/schedule/check/cancel/:id",
      authMiddleware(),
      TherapistController.askForPaymentOnCancellation
    );
    this.router.put(
      "/schedules/transaction/update/:scheduleRecId",
      authMiddleware(),
      TherapistController.updateTransactionByRecIdTherapistId
    );
    this.router.put(
      "/schedules/transaction/offline/:scheduleRecId",
      authMiddleware(),
      TherapistController.markTransactionPaidOffline
    );
    this.router.put(
      "/schedules/transaction/mark-offline/:_id",
      authMiddleware(),
      TherapistController.markTransactionPaidOfflineBy_id
    );
    this.router.put(
      "/calendar/update/syncDate",
      authMiddleware(),
      TherapistController.updateSyncDate
    );
    this.router.put(
      "/vpa/update",
      authMiddleware(),
      TherapistController.updateVpa
    );

    // DELETE
    this.router.delete(
      "/delete/:id",
      authMiddleware(),
      TherapistController.deleteTherapist
    );

    // Use
    this.router.use(
      "/paytracker",
      authMiddleware(),
      new PayTrackerRouter().router
    );

    // therapist onboarding from website
    this.router.post(
      "/onboarding",
      authMiddleware(),
      upload.single("file"),
      Validate(TherapistOnboardingSchema),
      TherapistController.therapistOnboarding
    );

    // therapist profile data update from website
    this.router.patch(
      "/onboarding",
      authMiddleware(),
      upload.single("file"),
      Validate(TherapistProfileUpdateSchema),
      TherapistController.therapistProfileUpdate
    );

    // therapist profile fetch from website
    this.router.get(
      "/onboarding",
      authMiddleware(),
      TherapistController.getTherapistProfile
    );

    // therapist working hours setup
    this.router.post(
      "/working-hours",
      authMiddleware(),
      Validate(TherapistWorkingHoursSchema),
      WorkingHoursController.setupWorkingHours
    );

    // therapist working hours update
    this.router.patch(
      "/working-hours",
      authMiddleware(),
      Validate(TherapistWorkingHoursSchema),
      WorkingHoursController.updateWorkingHours
    );

    // therapist working hours fetch
    this.router.get(
      "/working-hours",
      authMiddleware(),
      WorkingHoursController.getTherapistWorkingHours
    );

    // therapist specific working hours setup
    this.router.post(
      "/specific-working-hours",
      authMiddleware(),
      Validate(TherapistSpecificWorkingHoursSchema),
      WorkingHoursController.setupSpecificWorkingHours
    );

    // therapist specific working hours update
    this.router.patch(
      "/specific-working-hours/:id",
      authMiddleware(),
      Validate(TherapistSpecificWorkingHoursUpdateSchema),
      WorkingHoursController.updateSpecificWorkingHours
    );

    // therapist specific working hours fetch
    this.router.get(
      "/specific-working-hours",
      authMiddleware(),
      WorkingHoursController.getTherapistSpecificWorkingHours
    );

    // therapist all working hours fetch
    this.router.get(
      "/all-working-hours/:id",
      WorkingHoursController.getAllTherapistWorkingHours
    );

    this.router.post(
      "/session-booking",
      Validate(SessionBookingSchema),
      WorkingHoursController.sessionBooking
    );
  }
}
