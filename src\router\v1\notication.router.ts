import { Router } from "express";
import { Validate } from "../../lib/validations/validate";
import { authMiddleware } from "../../middleware/AuthMiddleware";
import { NoticationController } from "../../controller/notification.controller";
import { NotificationSchema, NotificationUpdateSchema } from "../../lib/validations/notification.schema";
import { adminAuthMiddleware } from "../../middleware/admin.auth.middleware";

export default class NotificationRouter {
    public router: Router;

    constructor(){
        this.router = Router();
        this.routes()
    }

    public routes(): void{
        // GET
        this.router.get("/admin/getAll", adminAuthMiddleware(), NoticationController.getAll);
        this.router.get("/admin/get/therapist/:therapistId", adminAuthMiddleware(), NoticationController.getByTherapistId);
        this.router.get("/therapist/get", authMiddleware(), NoticationController.getForTherapist);
        this.router.get("/therapist/all", authMiddleware(), NoticationController.getTherapistNotifications);

        // POST
        this.router.post("/admin/create", adminAuthMiddleware(), Validate(NotificationSchema), NoticationController.create);

        // PUT
        this.router.put("/admin/update/:id", adminAuthMiddleware(), Validate(NotificationUpdateSchema), NoticationController.update);

        // DELETE
        this.router.delete("/admin/delete/:id", adminAuthMiddleware(), NoticationController.delete);

    }
}