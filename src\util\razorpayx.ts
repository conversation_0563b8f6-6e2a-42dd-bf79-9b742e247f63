import axios, { AxiosRequestConfig, Method } from "axios";
import { CONFIG } from "../config/environment";

export class RazorpayX{
    static async makeRequest(url: string, method: Method, inputPayload?: any, queryData?: any) {


        let requestConfig: AxiosRequestConfig = {
            baseURL: CONFIG.razorpayx.baseUrl,
            url: url,
            method: method,
            headers: { 
                'Content-Type': 'application/json', 
            },
            auth: {
                username: CONFIG.razorpay.key_id,
                password: CONFIG.razorpay.key_secret,
            },
        };

        if (method !== 'get' && inputPayload) {
            requestConfig.data = inputPayload;
        }

        if (queryData) {
            requestConfig.params = queryData;
        }


        return await axios.request(requestConfig).then(res => {
            if (res.status === 200 || res.status === 201) {
                return res.data
            }
            else {
                console.log(res.data, "res")
                console.log(res.status, "res")
                return false
            }
        }).catch(e => {
            console.log(e, "errrr")
            return false
        });

    }
}