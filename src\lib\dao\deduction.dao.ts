import moment from "moment";
import { removeEmpty } from "../../helper/custom.helper";
import calendarEventModel from "../../models/calendarEvent.model";
import deductionModel, { DeductionTypeEnum } from "../../models/deduction.model";

export class DeductionDao {
    static async createDeduction(adminId: any, therapistId: any, amount: number, deductionType: any, deductionDate: any) {
        return await deductionModel.create({
            adminId,
            therapistId,
            amount,
            deductionType,
            deductionDate
        })
    }

    static async createRefundDeduction(therapistId: any, amount: number, scheduleId: any, scheduleRecId: any, clientId: any) {
        let deduction = new deductionModel();
        deduction.therapistId = therapistId;
        deduction.amount = amount;
        deduction.deductionType = DeductionTypeEnum.REFUNDTOCLIENT;
        deduction.scheduleId = scheduleId;
        deduction.scheduleRecId = scheduleRecId;
        deduction.clientId = clientId;
        deduction.deductionDate = new Date();
        deduction.remarks = "Refund to Client";
        return await deduction.save();
    }

    static async getAllDecution(pageSize: any, skip: any, therapistId: any, isNotPaid: any) {
        const query = removeEmpty({ therapistId: therapistId })
        if(isNotPaid == true || isNotPaid == 'true' ){
            query["payoutId"] = {
                '$exists': false
            }
        }
        return await deductionModel.find(query).skip(skip).limit(pageSize).populate({
            path: 'therapistId',
            select: 'name phone email'
        });
    }

    static async getDeductionById(id: any) {
        return await deductionModel.findById({_id: id}); 
    }

    static async updateDeduction(id: any, payload: any) {
        return await deductionModel.findByIdAndUpdate({_id: id, payoutId: {$exists: false}}, payload, {new: true}); 
    }

    static async deleteDeduction(id: any) {
        return await deductionModel.findByIdAndDelete({_id: id, payoutId: {$exist: false}}); 
    }

    static async getDeductionByIdAndTherapist(therapistId: any, id: any) {
        return await deductionModel.findOne({therapistId: therapistId, _id: id, payoutId: {$exists: false}}); 
    }

    static async updateDeductionForPayoutId(therapistId: any, id: any, payoutId: any) {
        return await deductionModel.findOneAndUpdate({therapistId: therapistId, _id: id}, {
            $set: {
                payoutId: payoutId
            }
        }, {new: true}); 
    }

    static async getCount(days: number){
        const date = moment().subtract(days, 'days').toDate();
        return await deductionModel.countDocuments({createdAt: {$gte: date}});
    }
    static async getDeductionAmount(days: number){
        const date = moment().subtract(days, 'days').toDate();
        const result = await deductionModel.aggregate([
            {
                $match: {
                    createdAt: {$gte: date}
                }
            },
            {
                $group: {
                    _id: null,
                    total: {$sum: "$amount"}
                }
            }
        ])
        return result[0].total;
    }

    static async createSessionCancelledDeduction(therapistId: any, amount: number, deductionType: any, deductionDate: any) {
        return await deductionModel.create({
            therapistId,
            amount,
            deductionType,
            deductionDate
        })
    }

    static async getDeductionsForExport(therapistId: any, startDate: any, endDate: any){
        return await deductionModel.find({
            therapistId: therapistId,
            deductionDate: {
                $gte: startDate,
                $lt: endDate
            }}).populate({
            path: 'clientId',
            select: 'clientId'
            })
    }
}