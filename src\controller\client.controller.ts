import { string } from "joi";
import express from "express";
import { Response } from "../util/response";
import { ClientService } from "../services/client.service";
import { TransactionService } from "../services/transaction.service";
import ClientModel from "../models/Client.model";
import { Utility } from "../util/util";
import { CONFIG } from "../config/environment";
import { Mailer2 } from "../util/mailer2";
import { MailSubjectEnum } from "../lib/enum/subject.enum";
import moment from "moment";
import { TherapistDao } from "../lib/dao/therapist.dao";
import otpModel from "../models/otp.model";
import { sendOtpForEmailVerification } from "../util/emailTemplate/sendVerificationOtp";

export class ClientController {
  static async create(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const payload = req.body;
      payload.therapistId = therapistId;
      const clientExist = await ClientService.getClientByEmail(
        payload.email,
        therapistId
      );
      if (clientExist) {
        return await res.send(
          new Response({ clientExist }, "client already exist.", 200)
        );
      }
      const client = await ClientService.create(payload);
      if (!client) {
        return res
          .status(404)
          .json({ success: false, message: `Client no created. ` });
      }
      res.send(new Response({ client }, "client created successfully. ", 200));
    } catch (error) {
      next(error);
    }
  }

  static async createMultipleClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const clientsData = req.body.clients;

      if (!Array.isArray(clientsData) || clientsData.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Invalid request. Please provide an array of clients.",
        });
      }

      let existingClients: string[] = [];
      let createdClients: string[] = [];

      for (const clientData of clientsData) {
        const clientExist = await ClientService.getClientByEmail(
          clientData.email,
          therapistId
        );

        if (clientExist) {
          existingClients.push(clientData.email);
        } else {
          const client = await ClientService.create({
            email: clientData.email,
            name: clientData.name,
            therapistId,
            phone: clientData.phone || "",
            defaultSessionAmount: clientData.defaultSessionAmount || "0",
            defaultTimezone: clientData.defaultTimezone || "Asia/Kolkata",
          });

          if (client) {
            createdClients.push(clientData.email);
          }
        }
      }

      return res.status(200).json({
        success: true,
        message: "Clients processed successfully.",
        existingClients,
        createdClients,
      });
    } catch (error) {
      console.error("Error creating clients:", error);
      next(error);
    }
  }

  static async getAllClient(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const clients = await ClientService.getAllClientsByTherapist(therapistId);
      res.send(new Response({ clients }, "clients fetched successfully.", 200));
    } catch (err) {
      next(err);
    }
  }

  static async getClientById(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const clientId = req.params.id;
      const client = await ClientService.getClientById(clientId);
      if (!client) {
        return res
          .status(404)
          .json({ success: false, message: `No client found.` });
      }
      const last_payment = await TransactionService.getLastTransactionOfClient(
        clientId,
        req.therapist._id
      );
      res.send(
        new Response(
          { client, last_payment },
          "client fetched successfully. ",
          200
        )
      );
    } catch (err) {
      next(err);
    }
  }

  static async findClientByEmail(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const clientEmail: any = req.query.email;
      const therapistId = req.therapist._id;
      const client = await ClientService.findClientByEmail(
        clientEmail,
        therapistId
      );
      res.send(client);
    } catch (err) {
      next(err);
    }
  }

  static async getClientByEmail(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.query.email as string;
      const therapistId = req.therapist._id;

      if (!email || email.length < 1) {
        return res.status(400).json({
          success: false,
          message: "Please provide at least one character to search.",
        });
      }

      const clients = await ClientModel.find({
        therapistId: therapistId,
        email: { $regex: `^${email}`, $options: "i" },
      });

      res.send(new Response({ clients }, "Clients fetched successfully.", 200));
    } catch (err) {
      next(err);
    }
  }

  static async sendOtp(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.body.email;

      const otp = await Utility.generateOTP();
      let senderData = {
        email: CONFIG.companyEmail,
        name: CONFIG.companyName,
      };
      let htmlTemplate = sendOtpForEmailVerification("There", otp);
      let receiverData = [
        {
          email: email,
          name: "There",
        },
      ];

      let subject = MailSubjectEnum.OTP_VERIFICATION;
      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );
      if (!sentEmail) {
        return res
          .status(400)
          .json({ success: false, message: "Sending Email Failed" });
      }

      // saving otp in otp collection
      const existingEmail = await otpModel.findOne({ email: email });
      if (existingEmail) {
        existingEmail.otpData.otp = otp;
        existingEmail.otpData.validTill = new Date(
          moment().add(30, "minutes").toISOString()
        );
        await existingEmail.save();
      } else {
        const newOtp = new otpModel({
          email: email,
          otpData: {
            otp: otp,
            validTill: new Date(moment().add(30, "minutes").toISOString()),
          },
        });
        await newOtp.save();
      }
      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async verifyOtp(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.body.email;
      const otp = req.body.otp;
      const existingEmail = await otpModel.findOne({ email: email });
      if (!existingEmail) {
        return res.status(400).json({
          success: false,
          message: "No client found with this email.",
        });
      }
      const isOtpValid = moment().isBefore(
        moment(existingEmail.otpData.validTill)
      );
      if (!isOtpValid) {
        return res
          .status(400)
          .json({ success: false, message: "OTP has expired." });
      }
      if (String(existingEmail.otpData.otp) != String(otp)) {
        return res.status(400).json({ success: false, message: "Wrong OTP." });
      }
      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async checkClient(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { clientEmail, therapistId }  = req.body;
      
      const client = await ClientService.findActiveClientByEmail(
        clientEmail,
        therapistId
      );
      let clientExists = false;
      if (client && client.length > 0) {
        clientExists = true;
      }
      res.status(200).json({ clientExists });
    } catch (err) {
      next(err);
    }
  }
}
