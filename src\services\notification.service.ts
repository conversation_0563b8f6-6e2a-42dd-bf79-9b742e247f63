import { NotificationDao } from "../lib/dao/notification.dao";

export class NotificationService {
    static async create(payload: any) {
        return await NotificationDao.create(payload)
    }

    static async update(_id: any, payload: any) {
        return await NotificationDao.update(_id, payload)
    }

    static async delete(_id: any) {
        return await NotificationDao.delete(_id)
    }

    static async getAll() {
        return await NotificationDao.getAll()
    }

    static async getByTherapistId(therapistId: any) {
        return await NotificationDao.getByTherapistId(therapistId)
    }

    static async updateNotification(_id: any) {
        return await NotificationDao.updateNotification(_id)
    }
}