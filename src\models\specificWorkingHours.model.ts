import { Schema, model, Document } from "mongoose";

interface ITimeSlot {
  startTime: String;
  endTime: String;
  duration: number;
}

export interface ITherapistSpecificWorkingHours extends Document {
  therapistId: string;
  date: string;
  slots: ITimeSlot[];
}

const TimeSlotSchema: Schema<ITimeSlot> = new Schema<ITimeSlot>({
  startTime: { type: String, required: true },
  endTime: { type: String, required: true },
  duration: { type: Number, required: true },
});

const TherapistSpecificWorkingHoursSchema: Schema<ITherapistSpecificWorkingHours> =
  new Schema<ITherapistSpecificWorkingHours>(
    {
      therapistId: { type: String, required: true },
      date: { type: String },
      slots: { type: [TimeSlotSchema] },
    },
    {
      versionKey: false,
      timestamps: true,
      collection: "therapistSpecificWorkingHour",
    }
  );

export default model<ITherapistSpecificWorkingHours>(
  "TherapistSpecificWorkingHour",
  TherapistSpecificWorkingHoursSchema
);
