import { Schema, model, Document } from "mongoose";

interface ITimeSlot {
  startTime: String;
  endTime: String;
  duration: number;
}

export interface ITherapistWorkingHours extends Document {
  therapistId: string;
  sunday: ITimeSlot[];
  monday: ITimeSlot[];
  tuesday: ITimeSlot[];
  wednesday: ITimeSlot[];
  thursday: ITimeSlot[];
  friday: ITimeSlot[];
  saturday: ITimeSlot[];
  date: string;
  slots: ITimeSlot[];
}

const TimeSlotSchema: Schema<ITimeSlot> = new Schema<ITimeSlot>({
  startTime: { type: String, required: true },
  endTime: { type: String, required: true },
  duration: { type: Number, required: true },
});

const TherapistWorkingHoursSchema: Schema<ITherapistWorkingHours> =
  new Schema<ITherapistWorkingHours>(
    {
      therapistId: { type: String, required: true },
      sunday: { type: [TimeSlotSchema] },
      monday: { type: [TimeSlotSchema] },
      tuesday: { type: [TimeSlotSchema] },
      wednesday: { type: [TimeSlotSchema] },
      thursday: { type: [TimeSlotSchema] },
      friday: { type: [TimeSlotSchema] },
      saturday: { type: [TimeSlotSchema] },
      date: { type: String },
      slots: { type: [TimeSlotSchema] },
    },
    {
      versionKey: false,
      timestamps: true,
      collection: "therapistWorkingHour",
    }
  );

export default model<ITherapistWorkingHours>(
  "TherapistWorkingHour",
  TherapistWorkingHoursSchema
);
