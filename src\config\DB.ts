import dotenv from "dotenv";
import mongoose from "mongoose";
import { IServer } from "../lib/interfaces/IServer";
import { CONFIG } from "./environment";
dotenv.config();

export class DB {
  static async connect(server?: IServer) {
    try {
      console.log("Connecting to <PERSON>");
      await mongoose.connect(CONFIG.DB_CONNECTION_STRING!);
      if (server) {
        server.isDbConnected = true;
      }
      console.log("Connected to <PERSON>");
    } catch (error) {
      throw error;
    }
  }
}
