export enum currentEnum {
    INR = "INR"
}

export enum paymentMethod {
    PAYTM = "PAYTM",
    RAZORPAY = "RAZORPAY",
    CASHFREE = "CASHFREE", 
    STRIPE = "STRIPE",
    FREE = "FREE"
}

export enum paymentStatus {
    COMPLETE = "COMPLETE",
    PENDING = "PENDING",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    PAID_OFFLINE = "PAID OFFLINE"
}


export enum RecieptType {
    GENERAL = "GENERAL",
    FINE = "FINE",
    ADJUSTMENT = "ADJUSTMENT"
}
