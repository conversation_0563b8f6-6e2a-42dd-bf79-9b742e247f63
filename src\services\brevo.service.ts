import axios, { AxiosRequestConfig, Method } from "axios";
import { CONFIG } from "../config/environment";

export class BrevoService {
    static async sendRequest(url: string, method: Method, payload?: any, queryData?: any): Promise<any> {
        try {
            const requestConfig: AxiosRequestConfig = {
                baseURL: CONFIG.brevo.baseUrl,
                url,
                method,
                headers: {
                    accept: "application/json",
                    "content-type": "application/json",
                    "api-key": CONFIG.brevo.apikey
                },
                ...(method !== "get" && payload ? { data: payload } : {}),
                ...(queryData ? { params: queryData } : {})
            };

            const response = await axios.request(requestConfig);
            return response.data;
        } catch (error: any) {
            console.error(`Brevo API Error (${method.toUpperCase()} ${url}):`, error?.response?.data || error.message);
            throw new Error(error?.response?.data?.message || "Brevo API request failed");
        }
    }

    static async sendMail(url: string, method: Method, inputPayload?: any, queryData?: any): Promise<any> {
        return this.sendRequest(url, method, inputPayload, queryData);
    }

    static async sendWhatsAppMessage(payload: any): Promise<any> {
        return this.sendRequest("/v3/whatsapp/sendMessage", "post", payload);
    }
}
