import workingHoursModel from "../../models/workingHours.model";
import specificWorkingHoursModel from "../../models/specificWorkingHours.model";

export class WorkingHoursDao {
  static async createWorkingHours(payload: any) {
    return await workingHoursModel.create(payload);
  }

  static async updateWorkingHours(payload: any, therapistId: string) {
    return await workingHoursModel.findOneAndUpdate(
      { therapistId },
      {
        $set: payload,
      },
      { new: true }
    );
  }

  static async getTherapistWorkingHours(id: string) {
    return await workingHoursModel.findOne({ therapistId: id });
  }

  static async createSpecificWorkingHours(
    payload: any,
    isSlotsAvailable: boolean
  ) {
    if (isSlotsAvailable) {
      // If record exists, push new slots to the existing slots array
      return await specificWorkingHoursModel.findOneAndUpdate(
        {
          therapistId: payload.therapistId,
          date: payload.date,
        },
        {
          $push: { slots: { $each: payload.slots } },
        },
        { new: true }
      );
    }

    // If no record exists, create a new one
    return await specificWorkingHoursModel.create(payload);
  }

  static async getSpecificWorkingHoursById(id: string) {
    return await specificWorkingHoursModel.findById(id);
  }

  static async getSpecificWorkingHoursByDate(
    therapistId: string,
    date: string
  ) {
    return await specificWorkingHoursModel.findOne({
      therapistId: therapistId,
      date: date,
    });
  }

  static async updateSpecificWorkingHours(payload: any, therapistId: string) {
    return await specificWorkingHoursModel.findOneAndUpdate(
      { therapistId, date: payload.date },
      {
        $set: payload,
      },
      { new: true }
    );
  }

  static async getTherapistSpecificWorkingHours(getCondition: any) {
    return await specificWorkingHoursModel.find(getCondition);
  }

  static async getTherapistAllWorkingHours(
    therapistId: string,
    durationCondition: any
  ) {
    const duration = durationCondition["slots.duration"];
    const pipeline = [
      {
        $match: { therapistId },
      },
      {
        $project: {
          _id: 1,
          therapistId: 1,
          sunday: {
            $filter: {
              input: "$sunday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
          monday: {
            $filter: {
              input: "$monday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
          tuesday: {
            $filter: {
              input: "$tuesday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
          wednesday: {
            $filter: {
              input: "$wednesday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
          thursday: {
            $filter: {
              input: "$thursday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
          friday: {
            $filter: {
              input: "$friday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
          saturday: {
            $filter: {
              input: "$saturday",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
        },
      },
    ];
    return await workingHoursModel.aggregate(pipeline);
  }

  static async getTherapistAllSpecificWorkingHours(getCondition: any) {
    const duration = getCondition["slots.duration"];
    const pipeline = [
      {
        $match: { therapistId: getCondition.therapistId },
      },
      {
        $project: {
          _id: 1,
          therapistId: 1,
          date: 1,
          slots: {
            $filter: {
              input: "$slots",
              as: "slot",
              cond:
                duration === 15
                  ? { $eq: ["$$slot.duration", 15] }
                  : { $ne: ["$$slot.duration", 15] },
            },
          },
        },
      },
    ];
    return await specificWorkingHoursModel.aggregate(pipeline);
  }
}
