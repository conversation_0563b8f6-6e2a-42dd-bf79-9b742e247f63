import { Schema, model, Document } from "mongoose";

export interface IOtp extends Document {
  email: string;
  otpData: {
    otp: string;
    validTill: Date;
  };
}

const otpSchema = new Schema<IOtp>(
  {
    email: { type: String },
    otpData: {
      otp: String,
      validTill: Date,
    },
  },
  {
    versionKey: false,
    timestamps: true,
    collection: "otp",
  }
);

export default model<IOtp>("otp", otpSchema);
