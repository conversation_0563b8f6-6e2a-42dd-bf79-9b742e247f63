import { TherapistDao } from "../lib/dao/therapist.dao";

export class TherapistService {
    static async createTherapist(payload: any) {
        return await TherapistDao.createTherapist(payload)
    }

    static async getTherapist(therapistId: any) {
        return await TherapistDao.getTherapist(therapistId)
    }

    static async updateTherapist(therapistId: any, therapistData: any) {
        return await TherapistDao.updateTherapist(therapistId, therapistData)
    }

    static async updateTherapistData(therapistId: any, therapistData: any) {
        return await TherapistDao.updateTherapistData(therapistId, therapistData)
    }

    static async deleteTherapist(therapistId: any) {
        return await TherapistDao.deleteTherapist(therapistId)
    }

    static async getAll(pageSize: number, skip: number, therapistId:any, isVerified: any) {
        return await TherapistDao.getAll(pageSize, skip, therapistId, isVerified)
    }

    static async getAllCount(isVerified: any) {
        return await TherapistDao.getAllCount(isVerified)
    }

    static async findByEmail(email: any) {
        return await TherapistDao.findByEmail(email)
    }

    static async getTherapistData(therapistId: any) {
        return await TherapistDao.getTherapistData(therapistId);
    }

    static async getCount(days?: number | undefined){
        return await TherapistDao.getCount(days);
    }

    static async getApprovedCount(){
        return await TherapistDao.getApprovedCount();
    }

    static async updateSyncDate(therapistId: any, syncData: any) {
        return await TherapistDao.updateSyncDate(therapistId, syncData)
    }
}