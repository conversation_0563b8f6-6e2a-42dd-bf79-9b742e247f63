import mongoose from "mongoose";
import {
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import ScheduleModel from "../models/Schedule.model";
import { PayTrackerService } from "./payTracker.service";
import moment from "moment";
import ClientModel from "../models/Client.model";

export class scriptPayTrackerService {
  static async findSessionWithoutPayTracker(therapistId: string) {
    const sessions = await ScheduleModel.aggregate([
      {
        $unwind: "$recurrenceDates",
      },
      {
        $match: {
          therapistId: new mongoose.Types.ObjectId(therapistId),
          "recurrenceDates.payTrackerId": { $exists: false },
          // "recurrenceDates.status": {
          //   $nin: ["cancelled", "completed"],
          // },
        },
      },
    ]);

    for (const session of sessions) {
      const paymentTracker = await PayTrackerService.createPayTracker({
        therapistId: therapistId,
        scheduleId: session?._id,
        scheduleRecId: session.recurrenceDates._id,
        clientId: session.clientId,
        dueDate: session.recurrenceDates.fromDate || "",
        amount: {
          currency: "INR",
          value: session.recurrenceDates.amount,
        },
        paymentType: PaymentTrackerTypeEnum.Post_Session,
        status: PaymentTrackerStatusEnum.Still_Pending,
        paymentDate: undefined,
        isDeleted: false,
        tags: [],
        sendRemainder: 0,
        isFine: false,
        cancellationFee: {
          currency: "INR",
          value: 0,
        },
      });

      await ScheduleModel.updateOne(
        {
          _id: session._id,
          "recurrenceDates._id": session.recurrenceDates._id,
        },
        {
          $set: {
            "recurrenceDates.$.payTrackerId": paymentTracker._id,
          },
        },
        {
          new: true,
        }
      );
    }

    return sessions;
  }

  static async addLocationAndRecurrence(therapistId: string) {
    const schedules = await ScheduleModel.aggregate([
      {
        $match: {
          therapistId: new mongoose.Types.ObjectId(therapistId),
          location: { $ne: "online" },
          recurrenceDates: { $ne: [] },
        },
      },
    ]);

    console.log(schedules.length);

    for (const schedule of schedules) {
      let recurrence;
      const repeatCounts: Record<number, number> = {};
      let mostRepeated = { dayDifference: 0, count: 0 };
      let day;
      const clientId = schedule.clientId;

      const client = await ClientModel.findOne({ _id: clientId });

      if (schedule.recurrenceDates.length >= 2) {
        day = moment(schedule.recurrenceDates[0]?.fromDate).format("dddd");
        for (let i = 0; i < schedule.recurrenceDates.length - 1; i++) {
          const currentDate: any = new Date(
            schedule.recurrenceDates[i]?.fromDate
          );
          const nextDate: any = new Date(
            schedule.recurrenceDates[i + 1]?.fromDate
          );

          const dayDifference: any = Math.ceil(
            (nextDate - currentDate) / (1000 * 60 * 60 * 24)
          );

          // Track repeat counts
          repeatCounts[dayDifference] = (repeatCounts[dayDifference] || 0) + 1;
        }

        mostRepeated = Object.entries(repeatCounts).reduce(
          (max, [key, value]) => {
            const dayDiff = Number(key); // Convert key back to a number
            return value > max.count
              ? { dayDifference: dayDiff, count: value }
              : max;
          },
          { dayDifference: 0, count: 0 } // Initial value
        );
      }

      if (schedule.recurrenceDates.length == 1) {
        recurrence = "Does Not Repeat";
      }

      if (mostRepeated.dayDifference == 7) {
        recurrence = `Every Week On ${day}`;
      } else if (mostRepeated.dayDifference == 14) {
        recurrence = `Every Two Weeks ${day}`;
      } else if (mostRepeated.dayDifference == 1) {
        recurrence = "Every Day";
      }

      const payload = {
        location: "online",
        recurrence: recurrence,
        name: client?.name || "",
        phone: client?.phone || "",
        age: client?.age || "",
        gender: client?.gender || "",
        clientCountry: "India",
        tillDate:
          moment(schedule.recurrenceDates[0]?.fromDate).add(3, "month") || "",
      };

      await ScheduleModel.findByIdAndUpdate(
        schedule._id,
        {
          $set: payload,
        },
        {
          new: true,
        }
      );
    }

    return schedules;

    // const scheduleWithLocation = schedule.map(async (sessionData) => {
    //   let recurrence;
    //   const repeatCounts: Record<number, number> = {};
    //   let mostRepeated = { dayDifference: 0, count: 0 };
    //   let day;
    //   const clientId = sessionData.clientId;

    //   const client = await ClientModel.findOne({ _id: clientId });

    //   if (sessionData.recurrenceDates.length >= 2) {
    //     day = moment(sessionData.recurrenceDates[0]?.fromDate).format("dddd");
    //     for (let i = 0; i < sessionData.recurrenceDates.length - 1; i++) {
    //       const currentDate: any = new Date(
    //         sessionData.recurrenceDates[i]?.fromDate
    //       );
    //       const nextDate: any = new Date(
    //         sessionData.recurrenceDates[i + 1]?.fromDate
    //       );

    //       const dayDifference: any = Math.ceil(
    //         (nextDate - currentDate) / (1000 * 60 * 60 * 24)
    //       );

    //       // Track repeat counts
    //       repeatCounts[dayDifference] = (repeatCounts[dayDifference] || 0) + 1;
    //     }

    //     mostRepeated = Object.entries(repeatCounts).reduce(
    //       (max, [key, value]) => {
    //         const dayDiff = Number(key); // Convert key back to a number
    //         return value > max.count
    //           ? { dayDifference: dayDiff, count: value }
    //           : max;
    //       },
    //       { dayDifference: 0, count: 0 } // Initial value
    //     );
    //   }

    //   if (sessionData.recurrenceDates.length == 1) {
    //     recurrence = "Does Not Repeat";
    //   }

    //   if (mostRepeated.dayDifference == 7) {
    //     recurrence = `Every Week On ${day}`;
    //   } else if (mostRepeated.dayDifference == 14) {
    //     recurrence = `Every Two Weeks ${day}`;
    //   } else if (mostRepeated.dayDifference == 1) {
    //     recurrence = "Every Day";
    //   }

    //   const payload = {
    //     location: "online",
    //     recurrence: recurrence,
    //     name: client?.name || "",
    //     phone: client?.phone || "",
    //     age: client?.age || "",
    //     gender: client?.gender || "",
    //     clientCountry: "India",
    //     tillDate:
    //       moment(sessionData.recurrenceDates[0]?.fromDate).add(3, "month") ||
    //       "",
    //   };

    //   const updatedSchedule = await ScheduleModel.findByIdAndUpdate(
    //     sessionData._id,
    //     {
    //       $set: payload,
    //     },
    //     {
    //       new: true,
    //     }
    //   );

    //   return updatedSchedule;
    // });

    // const recurrence = await Promise.all(scheduleWithLocation);

    // return recurrence;
  }
}
