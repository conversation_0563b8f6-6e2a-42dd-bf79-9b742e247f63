import { S3 } from 'aws-sdk';
import { CONFIG } from './environment';

export const s3Client = new S3({
    accessKeyId: CONFIG.blockStorage.accessKey,
    secretAccessKey: CONFIG.blockStorage.secretKey,
});

export const s3UploadParams: S3.PutObjectRequest = {
    Bucket: CONFIG.blockStorage.bucket_name || "",
    Key: "",
    Body: ""
}

export const s3DownloadParams: S3.GetObjectRequest = {
    Bucket: CONFIG.blockStorage.bucket_name || "",
    Key: ""
}

export const s3DownloadClient = new S3({
    accessKeyId: CONFIG.blockStorage.readOnlyAccessKey,
    secretAccessKey: CONFIG.blockStorage.readOnlySecretKey,
});

export function getBucket(): string {
    return CONFIG.blockStorage.bucket_name || "";
}

export const onboardingS3UploadParams: S3.PutObjectRequest = {
  Bucket: CONFIG.blockStorage.public_bucket_name || "",
  Key: "",
  Body: "",
};