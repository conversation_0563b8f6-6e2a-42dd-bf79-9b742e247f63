import { Utility } from '../util/util';
import { DB } from '../config/DB';
import AdminModel from '../models/Admin.model';

async function main() {
    await DB.connect();

    let adminEmail = `therapist_admin_${Math.floor(1000 + Math.random() * 9000)}`;
    let plainPassword = generatePassword();
    let password = Utility.createPasswordHash(plainPassword);
    let newAdmin = await AdminModel.create({ email: adminEmail, password: password });
    console.log('New Admin created');
    console.log('Email: ', adminEmail);
    console.log('Password: ', plainPassword);
}

function generatePassword() {
    var length = 8,
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
        retVal = "";
    for (var i = 0, n = charset.length; i < length; ++i) {
        retVal += charset.charAt(Math.floor(Math.random() * n));
    }
    return retVal;
}

main();
