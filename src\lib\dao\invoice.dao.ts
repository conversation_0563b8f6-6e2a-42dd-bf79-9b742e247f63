import moment from "moment";
import { removeEmpty, removeEmptyKeys } from "../../helper/custom.helper";
import TherapistModel from "../../models/Therapist.model";
import invoiceModel, { InvoiceCountryEnum } from "../../models/invoice.model";

export default class InvoiceDao {
    static async create(payload:any){
        return await invoiceModel.create(payload)
    }

    static async getLastInvoiceForTherapist(therapistId:any, isInternational: boolean){
        let query:any = {therapistId: therapistId}
        if(isInternational){
            query["invoiceCountry"] = {
                '$ne': InvoiceCountryEnum.INDIA
            }
        }
        else {
            query['invoiceCountry'] = InvoiceCountryEnum.INDIA
        }
        return await invoiceModel.find(query).sort({invoiceNumber: -1}).limit(1)
    }

    static async getAll(pageSize: any, skip: any, therapistId: any, clientId: any, isNotPaid: any, fromDate: Date, toDate: Date ){
        let query = removeEmpty({ therapistId: therapistId, clientId: clientId, createdAt: { 
            $gte: fromDate,
            $lt: toDate
        } })

        if(isNotPaid == true || isNotPaid == 'true' ){
            query["payoutId"] = {
                '$exists': false
            }
        }
       
        return await invoiceModel.find(query).skip(skip).limit(pageSize).populate({
            path: 'therapistId',
            select: 'name phone email'
        }).populate({
            path: 'clientId',
            select: 'clientId'
        }).populate({
            path: 'scheduleId',
        })
    }

    static async getCount(therapistId: any, clientId: any, isNotPaid: any, fromDate: Date, toDate: Date ){
        let query = removeEmpty({ therapistId: therapistId, clientId: clientId, createdAt: { 
            $gte: fromDate,
            $lt: toDate
        } })

        if(isNotPaid == true || isNotPaid == 'true' ){
            query["payoutId"] = {
                '$exists': false
            }
        }
       
        return await invoiceModel.find(query).count()
    }

    static async getBy_IdAndTherapist(therpaistId: any, id: any, ){
        return await invoiceModel.findOne({therapistId: therpaistId, _id: id, payoutId: {$exists: false}})
    }

    static async updateInvoice(therpaistId: any, id: any, payoutId: any){
        return await invoiceModel.findOneAndUpdate({therapistId: therpaistId, _id: id}, {
            $set: {
                payoutId: payoutId
            }
        }, {new: true})
    }

    static async getInvoicesForLastMonth(timeBefore30Days: any){
        const query = removeEmptyKeys({createdAt: {$gte: timeBefore30Days}})
        const invoices = await invoiceModel.find(query);
        const result = await invoiceModel.find({createdAt: {$gte: timeBefore30Days}});
        let last30DayInvoiceValue = 0;
        for(let invoice of invoices){
            const itemValue = invoice.itemTotal ? invoice.itemTotal : 0   
            last30DayInvoiceValue = last30DayInvoiceValue + itemValue
        }
        return {last30DayInvoices: result.length, last30DayInvoiceValue: last30DayInvoiceValue};
    }

    static async getAllInvoices(){
        return await invoiceModel.find().count()
    }

    static async getInvoiceById(id: any){
        return await invoiceModel.findOne({_id: id})
    }

    static async deleteInvoiceById(id: any){
        return await invoiceModel.findOneAndDelete({_id: id})
    }

    static async getInvoiceByScheduleRecId(scheduleRecId: any){
        return await invoiceModel.findOne({scheduleRecId: scheduleRecId})
    }


    static async getInvoicesForExport(therapistId:any, startDate:any, endDate:any){
        return await invoiceModel.find({therapistId: therapistId, createdAt: {
            $gte: startDate,
            $lt: endDate
        } }).populate({
            path: 'clientId',
            select: 'clientId'
        })
    }

}