import axios from "axios";
import { CONFIG } from "../config/environment";
import { BrevoService } from "../services/brevo.service";

export class BrevoWhatsapp {
  static async sendWhatsAppMessage(
    receiverData: { phone: string }[],
    templateId: number,
    params: Record<string, string>
  ): Promise<any> {
    const recipientNumber: string = receiverData[0]?.phone;
    if (!recipientNumber) {
      throw new Error("Recipient phone number is missing.");
    }

    const senderNumber = CONFIG.brevo.whatsappSender;
    if (!senderNumber) {
      throw new Error("Brevo WhatsApp sender number is not configured.");
    }

    // Corrected payload with params
    const payload = {
      senderNumber,
      contactNumbers: [recipientNumber],
      templateId,
      params, // Now includes dynamic parameters
    };

    try {
      // Send request using BrevoService
      const response = await BrevoService.sendWhatsAppMessage(payload);
      return response;
    } catch (error: any) {
      console.error(
        "Brevo WhatsApp API Error:",
        error?.response?.data || error.message
      );
      throw new Error(
        error?.response?.data?.message || "Failed to send WhatsApp message"
      );
    }
  }
}
