import crypto from 'crypto';

export class SymmetricCrypt {
    static async generateCipherData(data: string, key?: any) {

        if (!key) {
            throw new Error("Key is requried!")
        }

        const iv = (crypto.randomBytes(16)).toString('hex').slice(0, 16);

        const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

        let encryptedData = cipher.update(data, 'utf8', 'hex');

        encryptedData += cipher.final("hex");

        return {
            encryptedData: iv + ':' + encryptedData,
        }

    }


    static decipherData(encryptedData: string, key: any) {

        const splitData = encryptedData.split(':');
        const decipher = crypto.createDecipheriv('aes-256-cbc', key, splitData[0]);

        let decryptedData = decipher.update(splitData[1], "hex", "utf-8");

        decryptedData += decipher.final("utf8");

        return decryptedData;
    }
}