import Joi from "joi";
import { DefaultMessage } from "./message/default.message";

export const DeductionSchema: Joi.Schema = Joi.object({
    therapistId: Joi.string().optional().messages(DefaultMessage.defaultRequired("therapistId")),
    amount: Joi.number().optional().messages(DefaultMessage.defaultRequired("amount")),
    deductionType: Joi.string().optional().messages(DefaultMessage.defaultRequired("deductionType")),
    deductionDate: Joi.date().optional().messages(DefaultMessage.defaultRequired("deductionDate")),
});


export const PayoutUpdateSchema: Joi.Schema = Joi.object({
    payoutStatus: Joi.string().optional().messages(DefaultMessage.defaultRequired("payoutStatus")),
    transferDate: Joi.string().optional().messages(DefaultMessage.defaultRequired("transferDate")),
    payoutMode: Joi.string().optional().messages(DefaultMessage.defaultRequired("payoutMode")),
    refId: Joi.string().optional().messages(DefaultMessage.defaultRequired("Reference Id")),
});
