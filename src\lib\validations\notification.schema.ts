import joi from "joi"; 

export const NotificationSchema: joi.Schema = joi.object({
    therapistId: joi.string().required().messages({ "string.base": "therapistId must be a string", "any.required": "therapistId is a required field" }),
    message: joi.string().required().messages({ "string.base": "message must be a string", "any.required": "message is a required field" }),
})

export const NotificationUpdateSchema: joi.Schema = joi.object({
    isRead: joi.boolean().optional().messages({ "boolean.base": "isRead must be a boolean", "any.required": "isRead is a required field" }),
    message: joi.string().optional().messages({ "string.base": "message must be a string", "any.required": "message is a required field" }),
})