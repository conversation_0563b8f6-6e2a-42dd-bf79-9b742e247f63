import { AdminDao } from "../lib/dao/admin.dao";
import { TherapistDao } from "../lib/dao/therapist.dao";

export class AdminService{
    static async getByEmail(email: any){
        return await AdminDao.getByEmail(email)
    }

    static async updateBankDetails(therapistId: any, bankDetails: any) {
        return await AdminDao.updateBankDetails(therapistId, bankDetails);
    }

    static async getById(_id: any){
        return await AdminDao.getById(_id)
    }

    static async updateTherapist(therapistId: any, therapistData: any) {
        return await TherapistDao.updateTherapist(therapistId, therapistData)
    }
}