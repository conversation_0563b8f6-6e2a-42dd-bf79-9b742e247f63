import { Router } from "express";
import { scriptPayTrackerController } from "../../controller/scriptPayTracker.controller";

export default class payTrackerScriptRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    // GET
    //POST
    // PATCH

    // generate payTracker
    this.router.put(
      "/generate-payTracker/:therapistId",
      scriptPayTrackerController.generatePayTracker
    );

    // add location & recurrence
    this.router.put(
      "/location/:therapistId",
      scriptPayTrackerController.addLocationSchedule
    );
  }
}
