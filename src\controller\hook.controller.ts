import express from "express";
import { CONFIG } from "../config/environment";
import { TherapistService } from "../services/therapist.service";
import { CashfreePayments } from "../services/cashfreePayment.service";
import { TransactionService } from "../services/transaction.service";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import InvoiceService from "../services/invoice.service";
import { paymentMethod, paymentStatus } from "../lib/enum/cashfree.enum";
import { ScheduleStatus } from "../models/Schedule.model";
const stripe = require('stripe')(CONFIG.stripe.stripeSecretKEy);

export class HookController {
    static async createPaymentLink(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const data = req.body;
            const paymentLink = await CashfreePayments.createCashFreePaymentLink(data);
            res.send(paymentLink);
        }
        catch (error) {
            next(error)
        }
    }

    // static async batchTransfer(req: express.Request, res: express.Response, next: express.NextFunction){
    //     try{
    //         const data = req.body;
    //         const paymentLink = await CashfreePayments.batchTransfer();
    //         res.send(paymentLink);
    //     }
    //     catch(error){
    //         next(error)
    //     }
    // }

    // static async createStipePaymentLink(req: express.Request, res: express.Response, next: express.NextFunction) {
    //     try {
    //         const therapistId = req.params.therapistId;
    //         const therapist = await TherapistService.getTherapist(therapistId);
    //         if (!therapist) {
    //             return res.send(404).send("No Therapist Found.")
    //         }

    //         const amount = req.body.amount;
    //         const paymentLink = await StripeService.createPaymentLink((amount * 100), therapist);
    //         res.send(paymentLink);
    //     } catch (err) {
    //         next(err);
    //     }
    // }

    static async getAllStripePayments(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const paymentIntents = await stripe.paymentIntents.list({
                limit: 100,
            });

            res.send(paymentIntents.data);
        } catch (err) {
            next(err);
        }
    }

    // static async paymentSuccess(req: express.Request, res: express.Response, next: express.NextFunction) {
    //     try {
    //         const payload = req.body;
    //         if (req.body.type === "checkout.session.completed") {
    //             const transaction: any = await TransactionService.getTransactionWithCustomerId(payload.data.object.customer, payload.data.object.id);
    //             if (!transaction) {
    //                 return res.status(404).send(false);
    //             }
    //             const transactionDetails = await TransactionService.saveTransactionDetails(payload, transaction._id);
    //             const schedule: any = await ScheduleDao.updateRecurrenceStatusByTransactionId(transaction._id, transaction.scheduleRecId);
    //             const clientCountry = schedule.clientCountry === "India" ? false : true
    //             await InvoiceService.createInvoice(schedule?.therapistId, schedule?.clientId, schedule._id, transaction.scheduleRecId, clientCountry);
    //             return res.send(200);
    //         }
    //         res.send(200)
    //     } catch (err) {
    //         next(err);
    //     }
    // }

    static async rzpFullPaid (req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const paymentSuccessPayload: any = req.body;
            const rzp_id = paymentSuccessPayload.payload.payment_link.entity.reference_id;
            
            const transaction = await TransactionService.getTransactionBy_id(rzp_id);
            if (!transaction) {
                console.log("No Transaction Found with rzp_id: ", rzp_id)
                return res.status(200).send({
                    message: "No Transaction Found"
                });
            }

            const schedule = await ScheduleDao.getByRecurrenceDateId(transaction.scheduleRecId);
            if(!schedule){
                console.log("No Schedule Found with transactionId: ", transaction._id)
                return res.status(200).send({
                    message: "No Schedule Found"
                });
            }
            const recurrenceIdDataIndex = schedule?.recurrenceDates.findIndex((recurrence: any) => String(recurrence._id) == String(transaction.scheduleRecId));
            if(recurrenceIdDataIndex > -1 && (schedule?.recurrenceDates[recurrenceIdDataIndex]?.status != ScheduleStatus.CANCELLED)){
                schedule.recurrenceDates[recurrenceIdDataIndex].status = ScheduleStatus.CONFIRMED
            }
            
            transaction.paymentDetails = paymentSuccessPayload;
            transaction.amountReceived = (paymentSuccessPayload.payload.payment.entity.amount)/100;
            transaction.paymentDate = paymentSuccessPayload.payload.payment.entity.created_at;
            transaction.paymentStatus= paymentStatus.COMPLETE;
            transaction.paymentMethod = paymentMethod.RAZORPAY;
            transaction.gatewayCharges = {
                gatewayFee: (paymentSuccessPayload?.payload?.payment?.entity?.fee) ? paymentSuccessPayload?.payload?.payment?.entity?.fee/100 : 0,
                gatewayTax: (paymentSuccessPayload?.payload?.payment?.entity?.tax) ? paymentSuccessPayload?.payload?.payment?.entity?.tax/100 : 0
            }

            // const schedule = await ScheduleDao.updateRecurrenceStatusByTransactionId(transaction._id, transaction.scheduleRecId);

            const clientCountry = schedule.clientCountry != "India" ? true : false;

            const amount = (transaction.amountReceived) - (transaction.gatewayCharges.gatewayFee + transaction.gatewayCharges.gatewayTax);
            const actualValue = transaction.amountReceived;
            const invoice = await InvoiceService.createInvoice(schedule?.therapistId, schedule?.clientId, schedule._id, transaction.scheduleRecId, clientCountry, amount, actualValue);
            if(!invoice){
                console.log("Invoice not created for transactionId: ", transaction._id)
                return res.status(200).send({
                    message: "Invoice not created"
                });
            }
            invoice.transactionId = transaction._id;
            invoice.gatewayCharges = {
                gatewayFee: transaction.gatewayCharges.gatewayFee,
                gatewayTax: transaction.gatewayCharges.gatewayTax
            }
            await invoice.save();
            await transaction.save();
            await schedule.save()
            return res.send(200);
        } catch (err) {
            next(err);
        }
    }

    // static async refundPayment(req: express.Request, res: express.Response, next: express.NextFunction) {
    //     try {
    //         const transactionId = req.body.transactionId;
    //         const refund = await StripeService.refundPayment(transactionId);
    //         if (!refund) {
    //             return res.status(404).send("Refund no initiated. Something went wrong.")
    //         }
    //         res.send(refund)
    //     } catch (err) {
    //         next(err);
    //     }
    // }

    // static async paymentRefund(req: express.Request, res: express.Response, next: express.NextFunction) {
    //     try {
    //         const sig = req.headers['stripe-signature'];

    //         let event;
    //         console.log(req.body, "body")

    //         try {
    //             event = stripe.webhooks.constructEvent(String(req.body), sig, CONFIG.stripe.stripeSecretKEy);
    //             console.log(event, sig,"event")
    //         } catch (err: any) {
    //             console.log(err, "err")
    //             res.status(400).send(`Webhook Error: ${err.message}`);
    //             return;
    //         }

    //         // Handle the event
    //         if (event.type === 'charge.refunded') {
    //             const charge = event.data.object;
    //             console.log(charge, "charge")
    //             // Handle the refunded charge
    //             // You can perform actions here, like updating your database or sending notifications
    //         }

    //         // Return a response to acknowledge receipt of the event
    //         res.send({ received: true });
    //     } catch (err) {
    //         next(err);
    //     }
    // }
}