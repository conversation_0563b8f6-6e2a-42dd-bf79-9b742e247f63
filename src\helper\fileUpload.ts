import multer from 'multer';
import { CONFIG } from '../config/environment';
import path from 'path';
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, `${CONFIG.uploadsFolderPath}`)
    },
    filename: (req, file, cb) => {
        cb(null, new Date().getTime() + "_" + file.originalname);

    }
});

export const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1000 * 1000
    },
});

