import { Schema, model } from "mongoose";
import TherapistModel from "./Therapist.model";

interface IKey extends Document {
    therapist: Schema.Types.ObjectId
    therapistEmail: string
    key: string
}

const therapistSchema = new Schema<IKey>({
    therapist: { type: Schema.Types.ObjectId, ref: TherapistModel },
    therapistEmail: String,
    key: { type: String }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "key"
    }
);

export default model<IKey>("key", therapistSchema)