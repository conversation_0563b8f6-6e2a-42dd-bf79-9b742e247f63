import TherapistSubscriptionsModel from "../../models/TherapistSubscriptions.model";

export default class TherapistSubscriptionDao {
    static async getValidSubscriptions(therapistId: any) {
        return await TherapistSubscriptionsModel.find({ therapistId: therapistId, validTill: {$gte: new Date()}}).populate('subscriptionId').populate('subscriptionTransactionId')
    }

    static async create(data: any){
        return await TherapistSubscriptionsModel.create(data)
    }

    static async getAllSubscriptions(therapistId: any) {
        return await TherapistSubscriptionsModel.find({ therapistId: therapistId }).populate('subscriptionId').populate('subscriptionTransactionId')
    }

    static async getSubscriptionById(subscriptionId: any) {
        return await TherapistSubscriptionsModel.findById(subscriptionId).populate('subscriptionId').populate('subscriptionTransactionId')
    }

    // static async getActiveTillSubscriptionLastDate(therapistId: any){
    //     return await TherapistSubscriptionsModel.findOne({ therapistId: therapistId, validTill: {$gte: new Date()}}).sort({validTill: -1}).populate('subscriptionId').populate('subscriptionTransactionId').limit(1);
    // }

    static async getSubscriptionsPopulated(id:any){
        return await TherapistSubscriptionsModel.find({_id:id}).populate('therapistId').populate('subscriptionId').populate('subscriptionTransactionId');
    }

    static async getPaidByTherapistId(therapistId: any){
        return await TherapistSubscriptionsModel.find({ therapistId: therapistId  }).sort({createdAt: -1}).populate('subscriptionId').populate('subscriptionTransactionId')
    }
}