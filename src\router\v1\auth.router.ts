import { Router } from "express";
import { Validate } from "../../lib/validations/validate";
import { authMiddleware } from "../../middleware/AuthMiddleware";
import { ClientController } from "../../controller/client.controller";
import { ClientSchema } from "../../lib/validations/client.schema";
import { AdminController } from "../../controller/admin.controller";

export default class AuthRouter{
    public router: Router;

    constructor(){
        this.router = Router();
        this.routes()
    }

    public routes(): void{
        this.router.post("/admin/login", AdminController.login);

    }
}