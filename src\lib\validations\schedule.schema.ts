import Joi from "joi";
import { DefaultMessage } from "./message/default.message";

export const ScheduleSchema: Joi.Schema = Joi.object({
  emails: Joi.array()
    .required()
    .messages(DefaultMessage.defaultRequired("Emails")),
  clientCountry: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Client Country")),
  fromDate: Joi.any()
    .required()
    .messages(DefaultMessage.defaultRequired("From Date")),
  sessionDate: Joi.any()
    .required()
    .messages(DefaultMessage.defaultRequired("Session Date")),
  toDate: Joi.any().optional(),
  // .messages(DefaultMessage.defaultRequired("To Date")),
  name: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Name")),
  recurrence: Joi.string()
    .optional()
    .messages(DefaultMessage.defaultRequired("Recurrence")),
  tillDate: Joi.any()
    .optional()
    .messages(DefaultMessage.defaultRequired("Till Date")),
  description: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Description")),
  phone: Joi.optional().allow("", null),
  location: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Location")),
  gender: Joi.string().optional().allow("", null),
  age: Joi.string().optional().allow("", null),
  summary: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Summary")),
  amount: Joi.number()
    .required()
    .messages(DefaultMessage.defaultRequired("amount")),
  isBefore: Joi.boolean()
    .required()
    .messages(DefaultMessage.defaultRequired("isBefore")),
  collectYourself: Joi.boolean()
    .optional()
    .messages(DefaultMessage.defaultRequired("collectYourself")),
  timezone: Joi.string()
    .optional()
    .messages(DefaultMessage.defaultRequired("Time Zone")),
});
