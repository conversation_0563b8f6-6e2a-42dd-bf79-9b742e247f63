import bcrypt from "bcrypt";
import { CONFIG } from "../config/environment";
import jwt from "jsonwebtoken";
import moment from "moment";

export class Utility {
  static comparePasswordHash(hash: string, plainText: string) {
    return bcrypt.compareSync(plainText, hash);
  }

  static createPasswordHash(password: string) {
    let salt = bcrypt.genSaltSync(CONFIG.BCRYPT_SALT_ROUNDS);
    return bcrypt.hashSync(password, salt);
  }

  static generateJwtToken(userUUID: string) {
    return jwt.sign(
      {
        id: userUUID,
      },
      CONFIG.jwt.secret,
      { expiresIn: "1d" }
    );
  }

  static generateRandomString = () => {
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 8; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters.charAt(randomIndex);
    }
    return result;
  };

  static generateRandomNumber() {
    const min = 100000; // Minimum 5 digit number
    const max = 999999; // Maximum 5 digit number
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }

  static async createRandamAlphanumericString(n: number) {
    const string = (Math.random().toString(36) + "00000000000000000").slice(
      2,
      n + 2
    );
    return string;
  }

  static generateOTP = () => {
    const characters = "0123456789";
    let result = "";
    for (let i = 0; i < 7; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters.charAt(randomIndex);
    }
    return result;
  };

  static async findNearestFromDate(appointments: any) {
    let nearestAppointment = null;
    let minimumDifference: any = null;

    const now = moment();

    appointments.forEach((appointment: any) => {
      const fromDate = moment(appointment.fromDate);

      if (fromDate.isAfter(now)) {
        const difference = fromDate.diff(now);
        if (minimumDifference === null || difference < minimumDifference) {
          minimumDifference = difference;
          nearestAppointment = appointment;
        }
      }
    });

    return nearestAppointment;
  }

  static async findDifferences(arr1: Array<Object>, arr2: Array<Object>) {
    return arr1.filter((item1: any) => {
      return !arr2.some((item2: any) => {
        return item2._id.toString() === item1._id.toString();
      });
    });
  }

  static async getCurrentWeek(): Promise<{
    startDate: string;
    endDate: string;
  }> {
    const today = new Date();

    // Start date is today at 00:00:01 UTC
    const startDate = new Date(
      Date.UTC(
        today.getUTCFullYear(),
        today.getUTCMonth(),
        today.getUTCDate(),
        0,
        0,
        1,
        0
      )
    );

    // End date is 7 days from today at 23:59:59 UTC
    const endDate = new Date(
      Date.UTC(
        today.getUTCFullYear(),
        today.getUTCMonth(),
        today.getUTCDate() + 6,
        23,
        59,
        59,
        0
      )
    );

    // Return ISO string with time zone
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  }

  static async getCurrentDay(): Promise<{
    startDate: string;
    endDate: string;
  }> {
    const today = new Date();

    // Create start date: today's date at 00:00:01.000 UTC
    const startDate = new Date(
      Date.UTC(
        today.getUTCFullYear(),
        today.getUTCMonth(),
        today.getUTCDate(),
        0,
        0,
        1,
        0
      )
    );

    // Create end date: today's date at 23:59:59.000 UTC
    const endDate = new Date(
      Date.UTC(
        today.getUTCFullYear(),
        today.getUTCMonth(),
        today.getUTCDate(),
        23,
        59,
        59,
        0
      )
    );

    // Return ISO string with time zone (e.g., "2024-10-31T00:00:01.000Z")
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  }

  // static async allPossibleSlot(
  //   startDate: string,
  //   endDate: string
  // ): Promise<{ fromDate: Date; toDate: Date }[]> {
  //   const slots: { fromDate: Date; toDate: Date }[] = [];
  //   const start = new Date(startDate);
  //   const end = new Date(endDate);

  //   if (start > end) {
  //     throw new Error("Start date must be before or equal to end date.");
  //   }

  //   for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
  //     for (let hour = 9; hour <= 18; hour++) {
  //       const fromDate = new Date(d);
  //       fromDate.setHours(hour, 0, 0, 0);

  //       const toDate = new Date(fromDate);
  //       toDate.setHours(hour + 1);
  //       slots.push({ fromDate, toDate });
  //     }
  //   }

  //   return slots;
  // }

  static async allPossibleSlot(
    startDate: string,
    endDate: string,
    bookSlot: any
  ): Promise<{ fromDate: Date; toDate: Date }[]> {
    const slots: { fromDate: Date; toDate: Date }[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      throw new Error("Start date must be before or equal to end date.");
    }

    // for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    //   for (let hour = 9; hour <= 18; hour++) {
    //     const fromDate = new Date(d);
    //     fromDate.setHours(hour, 0, 0, 0);

    //     const toDate = new Date(fromDate);
    //     toDate.setHours(hour + 1);
    //     slots.push({ fromDate, toDate });
    //   }
    // }

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      for (let hour = 3; hour <= 12; hour++) {
        // Start with 30 minutes if it's the 3rd hour, otherwise start at 0 minutes
        for (let minute = hour === 3 ? 30 : 0; minute < 60; minute += 30) {
          const fromDate = new Date(d);
          fromDate.setHours(hour, minute, 0, 0);

          const toDate = new Date(fromDate);
          toDate.setMinutes(fromDate.getMinutes() + 60);

          // Stop if we've reached 12:30
          if (hour === 12 && minute === 30) break;

          slots.push({ fromDate, toDate });
        }
      }
    }

    return slots;
  }

  static addMinutes(date: Date, minutes: number) {
    return new Date(date.getTime() + minutes * 60000);
  }

  static isOverlapping(
    slot: { fromDate: Date; toDate: Date },
    booked: { fromDate: Date; toDate: Date }
  ): boolean {
    return (
      new Date(slot.fromDate) < new Date(booked.toDate) &&
      new Date(slot.toDate) > new Date(booked.fromDate)
    );
  }
  // static isOverlap(
  //   slot: { fromDate: Date; toDate: Date },
  //   booked: { recurrenceDates: { fromDate: Date; toDate: Date } }
  // ): boolean {
  //   // Check if the slot overlaps with the booked time
  //   return (
  //     slot.fromDate < booked.recurrenceDates.toDate &&
  //     slot.toDate > booked.recurrenceDates.fromDate
  //   );
  // }
  static isOverlap(
    slot: { fromDate: Date; toDate: Date },
    booked: { recurrenceDates: { fromDate: Date; toDate: Date } }
  ): boolean {
    const bookedFrom = new Date(booked.recurrenceDates.fromDate);
    const bookedTo = new Date(booked.recurrenceDates.toDate);

    // Add a 10-minute gap after the booked slot
    bookedFrom.setMinutes(bookedFrom.getMinutes() - 10);
    bookedTo.setMinutes(bookedTo.getMinutes() + 10);

    // Check if the slot overlaps with the adjusted booked time
    return slot.fromDate < bookedTo && slot.toDate > bookedFrom;
  }

  static isEventOverlap(
    slot: { fromDate: Date; toDate: Date },
    event: { fromDate: Date; toDate: Date }
  ): boolean {
    return slot.fromDate < event.toDate && slot.toDate > event.fromDate;
  }
}
